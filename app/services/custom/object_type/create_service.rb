class Custom::ObjectType::CreateService < ApplicationService
  def initialize(params, user: nil)
    @params = params
    @user = user
    @object_type = Custom::ObjectType.new
  end

  def execute
    ActiveRecord::Base.transaction do
      object_type_params = params.require(:data)
                                 .fetch(:attributes, {})
                                 .permit(:name, :icon, :color)

      object_type.assign_attributes(object_type_params)
      object_type.user = user
      object_type.icon ||= 'fa fa-wrench'
      object_type.color ||= '#03455a'

      object_type.save!

      object_type.object_type_user_relationships.create!(user: user, owner: true) if user

      assign_object_type_groups

      form_tab = object_type.form_tabs.create!(name_en: 'General', name_fr: '<PERSON><PERSON><PERSON><PERSON>', name_nl: 'Algemeen',
                                               name_es: 'General', position: 0)
      ota = object_type.object_type_attributes.create!(name_en: 'Name', name_fr: 'Nom', name_nl: 'Naam', key: 'name',
                                                       name_es: 'Nombre', data_type: 'String', field_identifier: 'name')
      form_tab.form_elements.create!(input_type: 'TextField', position: 0, object_type_attribute: ota)

      object_type.title_object_type_attribute = ota

      object_type.save!

      view = object_type.views.create!(name: 'Default', default: true)
      ObjectQueryView.create!(dependency: view)
      Visualisation.create!(
        view: view,
        view_type: 'objectTable',
        layout: {
          columns: %w[id name created_at team.name].map do |col|
            col == 'name' ? { object_type_attribute_id: ota.id.to_s } : { column_name: col }
          end
        }
      )
      all_view = object_type.views.create!(name: 'All', all_view: true)
      ObjectQueryView.create!(dependency: all_view)
      Visualisation.create!(view: all_view, view_type: 'objectTable', layout: { columns: [] })

      Custom::ObjectType::CreateDefaultObjectTypeRolesService.execute(object_type)

      object_type
    end
  end

  private

  attr_reader :params, :user, :object_type

  def assign_object_type_groups
    object_type_group_ids = params.dig(:data, :relationships, :object_type_groups, :data)&.pluck(:id)
    object_type_groups = ObjectTypeGroup.where(id: object_type_group_ids)
    object_type_groups.each do |group|
      object_type.object_type_object_type_group_relationships.create!(object_type_group: group)
    end
  end
end
