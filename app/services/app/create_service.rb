class App::CreateService < ApplicationService
  def initialize(payload, user:)
    @payload = payload
    @user = user
  end

  def execute
    ActiveRecord::Base.transaction do
      App.create!(**payload[:attributes]).tap do |app|
        app.app_user_relationships.create!(user: user, owner: true)

        payload[:relationships][:object_types].each do |object_type|
          object_type.object_type_app_relationships.create!(app: app)
        end
      end
    end
  end

  private

  attr_reader :payload, :user
end
