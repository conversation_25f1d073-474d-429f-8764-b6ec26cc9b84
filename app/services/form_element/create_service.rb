class FormElement::CreateService < ApplicationService
  include FindRelationshipsInIncluded

  def initialize(payload)
    @payload = payload
  end

  def execute
    form_tab = FormTab.find(payload.dig(:data, :relationships, :form_tab, :data, :id))
    form_element = form_tab.form_elements.build(form_element_params)

    ActiveRecord::Base.transaction do
      if object_type_attribute_params.present?
        attribute = Custom::ObjectTypeAttribute::CreateService.execute(object_type_attribute_params,
                                                                       object_type: form_tab.object_type)
        form_element.object_type_attribute = attribute
      end

      form_element.save!

      assign_attachments(form_element)

      form_tab.form_elements.where('position >= ?', form_element.position).where.not(id: form_element.id).increment_position
      form_tab.children.where('position >= ?', form_element.position).increment_position
    end

    calculation = form_element.object_type_attribute&.calculation

    ActiveRecord.after_all_transactions_commit do
      if calculation&.type_lookup?
        CalculationInitializeValuesWorker.perform_async(calculation.id)
      elsif calculation&.object_type_attribute&.active_calculation?
        Calculation::RecalculateWorker.perform_async(calculation.id)
      end
    end

    form_element
  end

  private

  attr_reader :payload

  def assign_attachments(form_element)
    return if payload_attachments.nil?

    form_element.attachments = payload_attachments.map do |attachment|
      form_element.create_or_update_attachment!(attachment)
    end
  end

  def payload_attachments
    @payload_attachments ||= begin
      data = payload.dig(:data, :relationships, :attachments, :data)
      find_relationships_in_included(data, payload[:included]) unless data.nil?
    end
  end

  def object_type_attribute_params
    @object_type_attribute_params ||= begin
      ota_ref = payload.dig(:data, :relationships, :object_type_attribute, :data)
      data = find_relationships_in_included(ota_ref, payload[:included]) if ota_ref
      if data
        data = data.deep_dup # avoid adapting the payload by deep cloning
        data[:included] = payload[:included]
      end
      data
    end
  end

  def form_element_params
    payload.require(:data)
           .fetch(:attributes, {})
           .permit(:input_type, :position, :custom_display, :placeholder, :team_filterable, :text,
                   :locked, :object_type_variant_id,
                   {
                     options: [
                       { select: :disabled }, { new_relationship_button: :disabled },
                       :attachment_upload_preference, :show_scan_button
                     ],
                     hidden: {}
                   })
  end
end
