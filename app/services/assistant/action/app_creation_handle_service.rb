class Assistant::Action::AppCreationHandleService < ApplicationService
  def initialize(action)
    @action = action
  end

  attr_reader :action

  def execute
    ActiveRecord::Base.transaction do
      message = action.message
      raise Assistant::ActionRuntimeError, 'Action message is not defined' if message.blank?

      data_model_part = message.find_tool_part('assistant_tools_suggest_data_model_tool__execute')
      raise Assistant::ActionRuntimeError, 'No data model found on message' if data_model_part.blank?

      # form_part = message.find_tool_part('assistant_tools_suggest_form_tool__execute')
      # raise Assistant::ActionRuntimeError, 'No form data found on message' if form_part.blank?

      template = {
        data_model: data_model_part.dig('output', 'data', 'objects')
        # forms: form_part.dig(:output, :data)
      }

      object_types = DataModelTemplate::ImportService.execute(template.with_indifferent_access, user: message.chat.user)

      object_types.each(&method(:create_form_for_object_type))

      { data: { object_type_ids: object_types.pluck(:id) } }
    end
  end

  def create_form_for_object_type(object_type)
    object_type.object_type_attributes.each do |ota|
      create_form_element(ota, object_type.form_tabs.first)
    end
  end

  def data_type_input_type_mapping
    @data_type_input_type_mapping ||= {
      String: 'TextField',
      Boolean: 'Checkbox',
      Date: 'Date',
      DateTime: 'DateTime',
      Number: 'TextField',
      HTML: 'HTML',
      Relationship: 'Relationship',
      Attachment: 'Attachment'
    }.with_indifferent_access
  end

  def create_form_element(attribute, section)
    input_type = data_type_input_type_mapping.with_indifferent_access[attribute.data_type] || 'TextField'
    section.form_elements.create!(position: section.form_elements.length, input_type: input_type, object_type_attribute: attribute)
  end
end
