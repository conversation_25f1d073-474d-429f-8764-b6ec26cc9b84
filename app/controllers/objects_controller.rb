class ObjectsController < ApplicationController
  MAX_OBJECTS = 500

  def index
    enforce_pagination!(max_page_size: MAX_OBJECTS)

    base_objects = Custom::Object.includes(
      [
        :object_attributes,
        :user,
        :last_updated_by,
        {
          object_type: :object_type_attributes,
          user: :letteravatar_blob,
          team_with_breadcrumb: [:object_type_role_team_relationships, :object_types, :ancestor_teams, :interface]
        }.merge(included_associations)
      ]
    )
    objects_query = Custom::Object::CollectionQuery.new(base_objects, params: params, user: current_user)

    options = {
      include: serializer_includes,
      meta: { total_count: objects_query.count }
    }
    render json: ObjectSerializer.new(paginate(objects_query.execute), options).serializable_hash
  end

  def show
    object = Custom::Object.includes(included_associations).find(params[:id])

    options = {
      include: serializer_includes
    }
    render json: ObjectSerializer.new(object, options).serializable_hash
  end

  def related_objects
    object = Custom::Object.find(params[:id])
    rel_ids = object.object_relationships.where(model_type: 'Custom::Object').pluck(:model_id)
    inverse_rel_ids = object.model_object_relationships.pluck(:object_id)
    result = Custom::Object.where(id: rel_ids + inverse_rel_ids).order(id: :desc).pluck(:id, :object_type_id)

    render json: result.map { |id, ot| { object_id: id.to_s, object_type_id: ot.to_s } }
  end

  def create
    object_type = Custom::ObjectType.find(params[:data][:relationships][:object_type][:data][:id])
    authorize({ object_type: object_type, team: object_team }, :create_object?, policy_class: TeamObjectTypePolicy)

    object, change_set = Custom::Object::CreateService.execute(
      object_type: object_type,
      user: current_user,
      payload: params&.to_unsafe_h
    )

    Custom::Object::EventHandlerService.execute(object, :create,
                                                user: current_user,
                                                change_set: change_set,
                                                message_id: params[:message_id])

    options = { include: serializer_includes }
    render json: ObjectSerializer.new(object, options).serializable_hash,
           status: :created,
           location: object_path(object)
  rescue ModelInvalidError => e
    render_error(e.errors.map do |error|
      { detail: error.message, code: 'InvalidAttribute' }.tap do |error_hash|
        error_hash[:meta] = error.meta if error.respond_to?(:meta)
      end
    end)
  end

  def update
    object = Custom::Object.includes(
      {
        object_type: [object_type_attributes: :linked_object_type_attributes]
      }.merge(included_associations)
    ).find(params[:id])
    authorize object, :update?

    payload = params&.to_unsafe_h

    authorize_team_update(object, payload) if payload.present?

    object, change_set = Custom::Object::UpdateService.execute(object, payload: payload, user: current_user)
    Custom::Object::EventHandlerService.execute(object, :update,
                                                user: current_user,
                                                change_set: change_set,
                                                message_id: params[:message_id])

    options = { include: serializer_includes }
    render json: ObjectSerializer.new(object, options).serializable_hash
  rescue ReadOnlyViolationError => e
    render_error([{ detail: e.message, code: 'ReadOnlyViolation' }])
  rescue ModelInvalidError => e
    render_error(e.errors.map do |error|
      { detail: error.message, code: 'InvalidAttribute' }.tap do |error_hash|
        error_hash[:meta] = error.meta if error.respond_to?(:meta)
      end
    end)
  end

  def destroy
    object = Custom::Object.includes(
      {
        object_type: [object_type_attributes: :linked_object_type_attributes]
      }
    ).find(params[:id])
    authorize object, :destroy?

    Custom::Object::EventHandlerService.execute(object, :destroy,
                                                message_id: params[:message_id],
                                                user: current_user)
    object.destroy!
    head :no_content
  end

  def export
    job_params = params.except(:object).permit(:name, filter: {}, context: {})
    query_rules = job_params.dig(:filter, :rules)
    job_params[:filter][:rules] = query_rules.index_by.with_index { |_, i| i } if query_rules.present?

    export_job = ExportWorker.queue_with_job!(user: current_user, parameters: job_params)
    render json: JobSerializer.new(export_job).serializable_hash
  end

  private

  def object_team
    team_payload = params.dig(:data, :relationships, :team)
    return if team_payload.blank?

    team_payload_id = team_payload.dig(:data, :id)
    Team.find(team_payload_id) if team_payload_id.present?
  end

  def included_associations
    related_object_includes = [
      :object_attributes,
      :user,
      :last_updated_by,
      :object_relationships,
      :inverse_object_relationships,
      { object_attachments: [file_attachment: :blob], object_type: [:object_type_attributes] }
    ]
    related_user_includes = [:letteravatar_blob]

    {
      object_attachments: [file_attachment: :blob],
      object_relationships: [model: related_object_includes + related_user_includes],
      inverse_object_relationships: [object: related_object_includes]
    }
  end

  def serializer_includes
    [
      :object_relationships,
      :'object_relationships.model',
      :'object_relationships.model.object_attachments',
      :inverse_object_relationships,
      :'inverse_object_relationships.object',
      :object_attachments,
      :team
    ]
  end

  def authorize_team_update(object, payload)
    team_payload_id = payload.dig(:data, :relationships, :team, :data, :id)
    return unless team_payload_id.present? && team_payload_id.to_s != object.team_id.to_s

    authorize(
      { team: Team.find(team_payload_id), object_type: object.object_type },
      :create_object?,
      policy_class: TeamObjectTypePolicy
    )
  end
end
