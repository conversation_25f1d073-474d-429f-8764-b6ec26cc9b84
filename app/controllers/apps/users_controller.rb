class Apps::UsersController < ApplicationController
  def index
    users = app.users.includes(:letteravatar_blob)
    users = users.where(app_user_relationships: { owner: owner_filter }) unless owner_filter.nil?

    users = User::CollectionQuery.execute(users, params: params)
    render json: UserSerializer.new(users).serializable_hash
  end

  def update
    authorize(app, :update?, policy_class: AppUsersPolicy)

    relationship = AppUserRelationship.find_or_initialize_by(user: user, app: app)

    relationship.update!(app_user_update_params)

    render json: UserSerializer.new(user).serializable_hash
  end

  private

  def app
    @app ||= App.find(params[:app_id])
  end

  def owner_filter
    ActiveModel::Type::Boolean.new.cast(params.dig(:filter, :owner))
  end

  def app_user_update_params
    params.fetch(:attributes, {})
          .permit(:owner)
  end

  def user
    @user ||= User.find(params[:id])
  end
end
