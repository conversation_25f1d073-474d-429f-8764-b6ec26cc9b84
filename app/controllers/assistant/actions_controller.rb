class Assistant::ActionsController < ApplicationController
  def create
    action = Assistant::Action::CreateService.execute(params)

    action.job = Assistant::ActionWorker.queue_with_job!(
      user: current_user,
      parameters: { action_id: action.id }
    )
    action.save

    render json: Assistant::ActionSerializer.new(action, { include: [:job] }).serializable_hash, status: :created
  end
end
