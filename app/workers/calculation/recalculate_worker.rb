class Calculation::RecalculateWorker
  include Sidekiq::Worker
  sidekiq_options queue: 'internal'
  sidekiq_options retry: 1
  sidekiq_options lock: :until_and_while_executing,
                  lock_ttl: 5.minutes.to_i,
                  on_conflict: { client: :replace, server: :reschedule }

  def perform(calculation_id)
    calculation = Calculation.find_by(id: calculation_id)
    return if calculation.blank?

    ota = calculation.object_type_attribute
    return unless ota.

    Calculation::RecalculateService.execute(calculation)
  end
end
