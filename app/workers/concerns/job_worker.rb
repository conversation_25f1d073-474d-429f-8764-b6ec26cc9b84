# Associates a worker with a Job record and wraps the worker execution to sync & broadcast Job state
# Usage:
# - once included and on worker execution in Sidekiq, JobWorker will call 'execute(job)' on your base class
# - return :failed if you want to job to fail silently
module JobWorker
  extend ActiveSupport::Concern

  class_methods do
    def queue_with_job!(scheduled_execution_time: nil, **attributes)
      job_type = name.delete_suffix('Worker')
      new_job = Job.new(
        job_type: job_type,
        scheduled_execution_time: scheduled_execution_time,
        **attributes
      )
      new_job.save!
      new_job.schedule!
      new_job
    end
  end

  included do |base|
    # broadcast failure after all retries have been exhausted
    base.sidekiq_retries_exhausted do |msg, _ex|
      Apartment::Tenant.switch(msg['apartment']) do
        job_id = msg['args'].first
        job = Job.find_by(id: job_id)
        if job.present?
          job.failed!
          job.broadcast
        end
      end
    end
  end

  def perform(job_id)
    job = Job.find_by(id: job_id)
    return if job.blank?

    I18n.with_locale(job.user&.language || 'en') do
      job.in_progress!
      if execute(job) == :failed
        job.failed!
      else
        job.completed!
      end
      job.broadcast
    rescue StandardError => e
      handle_perform_error(job)
      raise e
    end
  end

  def handle_perform_error(job)
    # don't update status or notify user on error if job is set to be retried
    retries = sidekiq_options_hash['retry']
    return if retries.present? && retries.positive?

    job.failed!
    job.broadcast
  end
end
