require 'rails_helper'

RSpec.describe AppUsersPolicy do
  let(:app) { create(:app) }

  permissions :update? do
    it 'denies access if user is not a global admin or an owner' do
      user = create(:user)
      create(:app_user_relationship, user: user, app: app, owner: false)

      expect(described_class).not_to permit(user, app)
    end

    it 'grants access if user is a global admin' do
      user = create(:user, admin: true)

      expect(described_class).to permit(user, app)
    end

    it 'grants access if user is an owner of the app' do
      user = create(:user)
      create(:app_user_relationship, user: user, app: app, owner: true)

      expect(described_class).to permit(user, app)
    end
  end
end
