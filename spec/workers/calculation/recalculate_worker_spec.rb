require 'rails_helper'

RSpec.describe Calculation::RecalculateWorker, type: :worker do
  describe 'sidekiq options' do
    it 'configures locking options' do
      expect(described_class.sidekiq_options_hash['lock']).to eq(:until_and_while_executing)
      expect(described_class.sidekiq_options_hash['lock_ttl']).to eq(5.minutes.to_i)

      conflict_options = described_class.sidekiq_options_hash['on_conflict']
      expect(conflict_options).to be_a(Hash)
      expect(conflict_options['client']).to eq(:replace)
      expect(conflict_options['server']).to eq(:reschedule)
    end

    it 'does not queue the same calculation again while it is still in progress' do
      # Sidekiq::Testing.disable! do
        calculation = create(:calculation)
        Calculation::RecalculateWorker.perform_async(calculation.id)
        Calculation::RecalculateWorker.perform_async(calculation.id)
        queued_jobs = Sidekiq::Queue.new('internal').to_a
        expect(queued_jobs.size).to be 1
      # end
    end
  end

  describe '#perform' do
    let(:object_type) { create(:object_type) }
    let(:attribute) { create(:object_type_attribute, object_type: object_type, data_type: 'Number', calculated: true) }
    let(:calculation) { create(:calculation, object_type_attribute: attribute) }

    it 'calls the Calculation::RecalculateService with the object type attribute' do
      allow(Calculation::RecalculateService).to receive(:execute)

      described_class.new.perform(calculation.id)

      expect(Calculation::RecalculateService).to have_received(:execute).with(calculation)
    end

    it 'does not call the Calculation::RecalculateService if the object type attribute does not exist' do
      allow(Calculation::RecalculateService).to receive(:execute)

      described_class.new.perform(0)

      expect(Calculation::RecalculateService).not_to have_received(:execute)
    end

    it 'does not call the Calculation::RecalculateService if it is not a calculated object type attribute' do
      allow(Calculation::RecalculateService).to receive(:execute)

      attribute.update(calculated: false)
      described_class.new.perform(calculation.id)

      expect(Calculation::RecalculateService).not_to have_received(:execute)
    end

    it 'does not call the Calculation::RecalculateService if the object type attribute is archived' do
      allow(Calculation::RecalculateService).to receive(:execute)

      attribute.update(archived: true)
      described_class.new.perform(calculation.id)

      expect(Calculation::RecalculateService).not_to have_received(:execute)
    end
  end
end
