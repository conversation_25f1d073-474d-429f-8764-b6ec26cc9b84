require 'rails_helper'

RSpec.describe 'ObjectsController' do
  describe 'GET #index' do
    context 'when user is signed in' do
      before { sign_in user }

      it_behaves_like 'it has pagination behaviour' do
        let(:url) { objects_url(host: host) }

        before { create_list(:object, 5) }
      end

      it 'is able to get Objects' do
        create_list(:object, 2)
        create_list(:object_type_attribute, 2, object_type: Custom::ObjectType.first)
        get objects_url(host: host), headers: headers
        expect(response).to have_http_status(:ok)
        expect(body['data'].length).to eq(2)
      end

      it 'is able to get Objects with a suggestion filter' do
        object_type = create(:object_type)
        attr1 = create(:object_type_attribute, object_type: object_type, key: 'name', searchable_for_recommendations: true)
        attr2 = create(:object_type_attribute, object_type: object_type, key: 'gibberish')
        object1 = create(:object, object_type: object_type)
        create(:object_attribute, object: object1, object_type_attribute: attr1, value: 'Fast food')
        object2 = create(:object, object_type: object_type)
        create(:object_attribute, object: object2, object_type_attribute: attr1, value: 'Fast pizza')
        object3 = create(:object, object_type: object_type)
        create(:object_attribute, object: object3, object_type_attribute: attr2, value: 'Fast gibberish')
        params = {
          filter: {
            object_type_id: object_type.id.to_s,
            suggestion: 'Fast'
          }
        }
        get objects_url(host: host), params: params, headers: headers
        expect(response).to have_http_status(:ok)
        expect(body['data'].length).to eq(2)
        expect(body['data'].pluck('id')).to contain_exactly(object1.id.to_s, object2.id.to_s)
      end

      it 'is able to get Objects with a ids filter' do
        object_type = create(:object_type)
        object1 = create(:object, object_type: object_type)
        object2 = create(:object, object_type: object_type)
        create(:object, object_type: object_type)
        params = {
          filter: {
            object_type_id: object_type.id.to_s,
            ids: [object1.id.to_s, object2.id.to_s]
          }
        }
        get objects_url(host: host), params: params, headers: headers
        expect(response).to have_http_status(:ok)
        expect(body['data'].length).to eq(2)
        expect(body['data'].pluck('id')).to contain_exactly(object1.id.to_s, object2.id.to_s)
      end

      it 'is able to get Objects with filter rules applied' do
        object_type = create(:object_type)
        team1, team2 = create_list(:team, 2)
        attribute = create(:object_type_attribute, object_type: object_type, key: 'name')
        object1 = create(:object, object_type: object_type, team: team1)
        create(:object_attribute, object: object1, object_type_attribute: attribute, value: 'something')
        object2 = create(:object, object_type: object_type, team: team2)
        create(:object_attribute, object: object2, object_type_attribute: attribute, value: 'nothing')
        object3 = create(:object, object_type: object_type, team: team1)
        create(:object_attribute, object: object3, object_type_attribute: attribute, value: 'random')

        params = {
          filter: {
            object_type_id: object_type.id.to_s,
            rules: {
              # This format is how the frontend sends array data through the url
              0 => { rule_type: 'filter', value: 'thing', operator: 'contains', object_type_attribute_id: attribute.id.to_s },
              1 => { rule_type: 'filter', value: team1.id.to_s, operator: 'equals', column_name: 'team_id' }
            }
          }
        }

        get objects_url(host: host, params: params), headers: headers
        expect(response).to have_http_status(:ok)
        expect(body['data'].length).to eq(1)
        expect(body['data'].pluck('id')).to contain_exactly(object1.id.to_s)
      end

      it 'is able to get Objects with nested filter rules applied' do
        object_type = create(:object_type)
        attribute = create(:object_type_attribute, object_type: object_type, key: 'name')
        object1 = create(:object, object_type: object_type)
        create(:object_attribute, object: object1, object_type_attribute: attribute, value: 'something')
        object2 = create(:object, object_type: object_type)
        create(:object_attribute, object: object2, object_type_attribute: attribute, value: 'nothing')
        object3 = create(:object, object_type: object_type)
        create(:object_attribute, object: object3, object_type_attribute: attribute, value: 'random')

        params = {
          filter: {
            object_type_id: object_type.id.to_s,
            rules: {
              # This format is how the frontend sends array data through the url
              0 => {
                0 => { rule_type: 'filter', value: 'thing', operator: 'contains', object_type_attribute_id: attribute.id.to_s }
              },
              1 => {
                0 => { rule_type: 'filter', value: 'm', operator: 'contains', object_type_attribute_id: attribute.id.to_s }
              }
            }
          }
        }

        get objects_url(host: host, params: params), headers: headers
        expect(response).to have_http_status(:ok)
        expect(body['data'].length).to eq(1)
        expect(body['data'].pluck('id')).to contain_exactly(object1.id.to_s)
      end
    end

    context 'when user is not signed in' do
      it 'is not able to get Objects' do
        get objects_url(host: host), headers: headers
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'GET #show' do
    context 'when user is signed in' do
      before { sign_in user }

      it 'is able to get an Object' do
        object = create(:object)
        get object_url(object, host: host), headers: headers
        expect(response).to have_http_status(:ok)
        expect(body['data']['id']).to eq(object.id.to_s)
      end
    end

    context 'when user is not signed in' do
      it 'is not able to get an Object' do
        object = create(:object)
        get object_url(object, host: host), headers: headers
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'GET #related_objects' do
    context 'when user is signed in' do
      before { sign_in user }

      it 'is able to get an objects related objects' do
        object = create(:object)
        object2 = create(:object)
        object3 = create(:object)
        # unrelated object to check if it isn't added to related ones
        create(:object)
        create(:object_relationship, object: object2, model: object)
        create(:object_relationship, object: object, model: object3)
        get related_objects_object_url(object, host: host), headers: headers
        expect(response).to have_http_status(:ok)
        expect(body[0]['object_id']).to eq(object3.id.to_s)
        expect(body[0]['object_type_id']).to eq(object3.object_type.id.to_s)
        expect(body[1]['object_id']).to eq(object2.id.to_s)
        expect(body[1]['object_type_id']).to eq(object2.object_type.id.to_s)
        expect(body.length).to eq(2)
      end
    end

    context 'when user is not signed in' do
      it 'is unable to get an objects related objects' do
        object = create(:object)
        get object_url(object, host: host), headers: headers
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'POST #create' do
    context 'when user is signed in' do
      before { sign_in user }

      context 'when user has object create rights for the object team and object type' do
        let(:object_type) { create(:object_type) }
        let(:role) do
          create(:object_type_role, :creator, object_type: object_type, name: 'creator', can_create: true, through_team: true)
        end
        let(:object_team) { create(:team) }

        before do
          team = create(:team)
          user.teams << team
          role.object_type_role_team_relationships.create!(team: team, object_scope_team: object_team)
        end

        it 'is able to create an Object' do
          create(:object_type_attribute, object_type: object_type, key: 'name')
          create(:object_type_attribute, object_type: object_type, key: 'location')
          create(:object_type_attribute, object_type: object_type, key: 'type')
          params = {
            data: {
              attributes: {
                values: {
                  name: 'Houston',
                  location: 'The Moon'
                }
              },
              relationships: {
                object_type: {
                  data: {
                    id: object_type.id.to_s,
                    type: 'object_type'
                  }
                },
                team: {
                  data: {
                    id: object_team.id.to_s,
                    type: 'team'
                  }
                }
              }
            }
          }
          post objects_url(host: host), params: params.to_json, headers: headers
          expect(response).to have_http_status(:created)
          expect(body['data']['id']).to be_present
          expect(body['data']['attributes']['values']['name']).to eq('Houston')
          expect(body['data']['attributes']['values']['location']).to eq('The Moon')
          expect(body['data']['attributes']['values']['type']).to be_nil

          object = Custom::Object.find(body['data']['id'])
          expect(object.snapshots.count).to eq 1
          expect(object.snapshots.last.event).to eq 'create'
          expect(object.team).to eq(object_team)
        end

        it 'is able to create an Object with an attribute having a default value' do
          create(:object_type_attribute, object_type: object_type,
                                         key: 'name',
                                         default_value: [{ value: 'DHH', dynamic: false }])
          params = {
            data: {
              attributes: {
                values: {}
              },
              relationships: {
                object_type: {
                  data: {
                    id: object_type.id.to_s,
                    type: 'object_type'
                  }
                },
                team: {
                  data: {
                    id: object_team.id.to_s,
                    type: 'team'
                  }
                }
              }
            }
          }
          post objects_url(host: host), params: params.to_json, headers: headers
          expect(response).to have_http_status(:created)
          expect(body['data']['id']).to be_present
          expect(body['data']['attributes']['values']['name']).to eq('DHH')
        end

        it 'is able to create an Object without team if a role grants that right' do
          role.object_type_role_team_relationships.first.update!(object_scope_team: nil)
          params = {
            data: {
              attributes: {
                values: {}
              },
              relationships: {
                object_type: {
                  data: {
                    id: object_type.id.to_s,
                    type: 'object_type'
                  }
                }
              }
            }
          }
          post objects_url(host: host), params: params.to_json, headers: headers
          expect(response).to have_http_status(:created)
          expect(body['data']['id']).to be_present
        end

        it 'is not able to create an Object with validation errors' do
          ota = create(:object_type_attribute, object_type: object_type, name: 'Name', key: 'name')
          create(:object_type_attribute_validation, object_type_attribute: ota, validation_type: 'min_length', value: 20)
          params = {
            data: {
              attributes: {
                values: {
                  name: 'Houston'
                }
              },
              relationships: {
                object_type: {
                  data: {
                    id: object_type.id.to_s,
                    type: 'object_type'
                  }
                },
                team: {
                  data: {
                    id: object_team.id.to_s,
                    type: 'team'
                  }
                }
              }
            }
          }
          post objects_url(host: host), params: params.to_json, headers: headers
          expect(response).to have_http_status(:unprocessable_entity)
          expect(body['errors']).to contain_exactly(
            {
              'detail' => "Name #{I18n.t(:'errors.messages.too_short', count: 20)}",
              'code' => 'InvalidAttribute',
              'meta' => { 'object_type_attribute_id' => ota.id }
            }
          )
        end
      end

      context 'when user has no object create rights for the team and object type' do
        let(:object_type) { create(:object_type) }
        let(:role) do
          create(:object_type_role, :creator, object_type: object_type, name: 'creator', can_create: true, through_team: true)
        end
        let(:object_team) { create(:team) }

        before do
          team = create(:team)
          user.teams << team
          # user team has create rights, but only for an unrelated object_scope_team
          role.object_type_role_team_relationships.create!(team: team, object_scope_team: create(:team))
        end

        it 'is not able to create an Object for a team' do
          params = {
            data: {
              attributes: {
                values: {}
              },
              relationships: {
                object_type: {
                  data: {
                    id: object_type.id.to_s,
                    type: 'object_type'
                  }
                },
                team: {
                  data: {
                    id: object_team.id.to_s,
                    type: 'team'
                  }
                }
              }
            }
          }
          object_count = Custom::Object.count
          post objects_url(host: host), params: params.to_json, headers: headers
          expect(response).to have_http_status(:forbidden)
          expect(Custom::Object.count).to eq object_count
        end

        it 'is not able to create an Object without a team' do
          params = {
            data: {
              attributes: {
                values: {}
              },
              relationships: {
                object_type: {
                  data: {
                    id: object_type.id.to_s,
                    type: 'object_type'
                  }
                }
              }
            }
          }
          object_count = Custom::Object.count
          post objects_url(host: host), params: params.to_json, headers: headers
          expect(response).to have_http_status(:forbidden)
          expect(Custom::Object.count).to eq object_count
        end
      end
    end

    context 'when user is not signed in' do
      it 'is not able to create an Object' do
        object_type = create(:object_type)
        params = {
          data: {
            attributes: {},
            relationships: {
              object_type: {
                data: {
                  id: object_type.id.to_s,
                  type: 'object_type'
                }
              }
            }
          }
        }
        post objects_url(host: host), params: params.to_json, headers: headers
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'PUT #update' do
    context 'when user is signed in' do
      let(:object_type) { create(:object_type) }

      before { sign_in user }

      context 'when user has update rights on the object' do
        let(:role) do
          create(:object_type_role, :editor, object_type: object_type, name: 'updater', can_edit: true, through_team: true)
        end
        let(:role2) do
          create(:object_type_role, :editor, object_type: object_type, name: 'updater', can_edit: true, through_team: true)
        end
        let(:object_team) { create(:team, name: 'Object Team') }
        let(:team1) { create(:team, name: 'Team 1') }
        let(:team2) { create(:team, name: 'Team 2') }

        before do
          user.teams << team1
          role.object_type_role_team_relationships.create!(team: team1, object_scope_team: object_team)

          user.teams << team2
          role2.object_type_role_team_relationships.create!(team: team2, object_scope_team: object_team)
        end

        it 'is able to update an Object' do
          create(:object_type_attribute, object_type: object_type, key: 'name')
          attr2 = create(:object_type_attribute, object_type: object_type, key: 'location')
          attr3 = create(:object_type_attribute, object_type: object_type, key: 'type')
          object = create(:object, object_type: object_type, team: object_team)
          create(:object_attribute, object: object, object_type_attribute: attr2, value: 'Space')
          create(:object_attribute, object: object, object_type_attribute: attr3, value: 'Space Shuttle')
          params = {
            data: {
              id: object.id.to_s,
              attributes: {
                values: {
                  name: 'Houston',
                  location: 'Unknown'
                }
              }
            }
          }
          put object_url(object, host: host), params: params.to_json, headers: headers
          expect(response).to have_http_status(:ok)

          expect(body['data']['attributes']['values']['name']).to eq('Houston')
          expect(body['data']['attributes']['values']['location']).to eq('Unknown')
          expect(body['data']['attributes']['values']['type']).to eq('Space Shuttle')

          expect(object.snapshots.count).to eq 1
          expect(object.snapshots.last.event).to eq 'update'
          expect(object.reload.team).to eq(object_team)
        end

        it "doesn't update the object and rollbacks all updates when an error is raised" do
          attr1 = create(:object_type_attribute, object_type: object_type, key: 'name')
          attr2 = create(:object_type_attribute, object_type: object_type, key: 'location', can_update: false)
          object = create(:object, object_type: object_type, team: object_team)
          create(:object_attribute, object: object, object_type_attribute: attr1, value: 'Houston')
          create(:object_attribute, object: object, object_type_attribute: attr2, value: 'Space Shuttle')
          params = {
            data: {
              id: object.id.to_s,
              attributes: {
                values: {
                  name: 'Austin',
                  location: 'Powers'
                }
              }
            }
          }
          put object_url(object, host: host), params: params.to_json, headers: headers
          expect(response).to have_http_status(:unprocessable_entity)

          object.reload
          expect(object.object_attributes.map(&:value)).to contain_exactly('Houston', 'Space Shuttle')
        end

        it 'is able to update to a team where the user has write permission' do
          create(:object_type_attribute, object_type: object_type, key: 'name')
          attr2 = create(:object_type_attribute, object_type: object_type, key: 'location')
          attr3 = create(:object_type_attribute, object_type: object_type, key: 'type')
          object = create(:object, object_type: object_type, team: object_team)
          create(:object_attribute, object: object, object_type_attribute: attr2, value: 'Space')
          create(:object_attribute, object: object, object_type_attribute: attr3, value: 'Space Shuttle')
          new_team = team2
          params = {
            data: {
              id: object.id.to_s,
              attributes: {
                values: {
                  name: 'Houston',
                  location: 'Unknown'
                }
              },
              relationships: {
                team: {
                  data: {
                    id: new_team.id.to_s,
                    type: 'team'
                  }
                }
              }
            }
          }
          put object_url(object, host: host), params: params.to_json, headers: headers
          expect(response).to have_http_status(:ok)

          expect(body['data']['attributes']['values']['name']).to eq('Houston')
          expect(body['data']['attributes']['values']['location']).to eq('Unknown')
          expect(body['data']['attributes']['values']['type']).to eq('Space Shuttle')

          expect(object.snapshots.count).to eq 1
          expect(object.snapshots.last.event).to eq 'update'
          expect(object.reload.team).to eq(new_team)
        end
      end

      context 'when user has no update rights on the object' do
        it 'is not able to update an Object' do
          create(:object_type_attribute, object_type: object_type, key: 'name')
          object = create(:object, object_type: object_type)
          params = {
            data: {
              id: object.id.to_s,
              attributes: {
                values: {
                  name: 'Houston'
                }
              }
            }
          }
          put object_url(object, host: host), params: params.to_json, headers: headers
          expect(response).to have_http_status(:forbidden)
        end
      end
    end

    context 'when user is not signed in' do
      it 'is not able to update an Object' do
        object = create(:object)
        params = {
          data: {
            id: object.id.to_s,
            attributes: {}
          }
        }
        put object_url(object, host: host), params: params.to_json, headers: headers
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'DELETE #destroy' do
    context 'when user is signed in' do
      before { sign_in user }

      context 'when user has delete rights on the object' do
        let(:object_type) { create(:object_type) }
        let(:role) do
          create(:object_type_role, :author, object_type: object_type, name: 'deleter', can_delete: true, through_team: true)
        end
        let(:object_team) { create(:team) }

        before do
          team = create(:team)
          user.teams << team
          role.object_type_role_team_relationships.create!(team: team, object_scope_team: object_team)
        end

        it 'is able to destroy an Object' do
          object = create(:object, object_type: object_type, team: object_team)
          delete object_url(object, host: host), headers: headers
          expect(response).to have_http_status(:no_content)
          expect { Custom::Object.find(object.id) }.to raise_error ActiveRecord::RecordNotFound

          expect(Snapshot.where(item: object).count).to eq 1
          snapshot = Snapshot.find_by(item: object)
          expect(snapshot.event).to eq 'destroy'
          expect(snapshot.user_id).to eq user.id
        end
      end

      context 'when user does not have delete rights on the object' do
        it 'is not able to destroy an Object' do
          object = create(:object)
          delete object_url(object, host: host), headers: headers
          expect(response).to have_http_status(:forbidden)
        end
      end
    end

    context 'when user is not signed in' do
      it 'is not able to destroy an Object' do
        object = create(:object)
        delete object_url(object, host: host), headers: headers
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'POST #export' do
    context 'when user is signed in' do
      before { sign_in user }

      it 'is able to create an Export Job' do
        params = { 'filter' => { 'object_type_id' => 5 } }
        post export_objects_url(host: host), params: params.to_json, headers: headers
        expect(response).to have_http_status(:ok)
        job = Job.find(body['data']['id'])
        expect(job.parameters).to eq params
      end
    end

    context 'when user is not signed in' do
      it 'is not able to create an Export Job' do
        post export_objects_url(host: host), headers: headers
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
