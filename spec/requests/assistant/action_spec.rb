require 'rails_helper'

RSpec.describe 'ChatsController' do
  describe 'POST #create' do
    context 'when user is signed in' do
      before { sign_in user }

      it 'is able to create an action' do
        message = create(:assistant_message)
        params = {
          data: {
            attributes: {
              action_type: 'app_creation'
            },
            relationships: {
              message: {
                data: {
                  id: message.id.to_s,
                  type: 'assistant_message'
                }
              }
            },
            type: 'assistant_action'
          }
        }

        post assistant_actions_url(host: host), params: params.to_json, headers: headers
        expect(response).to have_http_status(:created)
        action = Assistant::Action.find(body['data']['id'])
        expect(action.action_type).to eq 'app_creation'
        expect(action.message).to eq message
        expect(action.job).to be_present
        expect(action.job.user).to eq(user)
      end
    end

    context 'when user is not signed in' do
      it 'is not able to create an action' do
        post assistant_actions_url(host: host)
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
