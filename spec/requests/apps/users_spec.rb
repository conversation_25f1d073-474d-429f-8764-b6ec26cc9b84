require 'rails_helper'

RSpec.describe 'Apps::UsersController' do
  describe 'GET #index' do
    before { sign_in user }

    it 'returns app users' do
      app = create(:app)
      create(:app_user_relationship, user: user, app: app, owner: true)
      users = create_list(:user, 2)
      users.each { |u| create(:app_user_relationship, user: u, app: app) }
      create(:user)
      get app_users_url(app, host: host), headers: headers

      expect(response).to have_http_status(:ok)
      expect(body['data'].length).to eq 3
      expect(body['data'].pluck(:id)).to eq ([user] + users).pluck(:id).map(&:to_s)
    end

    it 'returns app users filtered on ownership' do
      app = create(:app)
      create(:app_user_relationship, user: user, app: app, owner: true)
      create_list(:app_user_relationship, 2, app: app)
      get app_users_url(app, host: host), params: { filter: { owner: true } }, headers: headers
      expect(response).to have_http_status(:ok)
      expect(body['data'].length).to eq 1
      expect(body['data'].pluck(:id)).to eq [user.id].map(&:to_s)
    end

    it 'can filter on additional conditions too' do
      app = create(:app)
      create(:app_user_relationship, user: user, app: app, owner: true)
      create(:app_user_relationship, app: app, owner: true)
      get app_users_url(app, host: host), params: { filter: { owner: true, ids: user.id.to_s } }, headers: headers
      expect(response).to have_http_status(:ok)
      expect(body['data'].length).to eq 1
      expect(body['data'].pluck(:id)).to contain_exactly(user.id.to_s)
    end
  end

  describe 'PATCH #update' do
    before { sign_in user }

    it 'updates the user app relationship' do
      app = create(:app)
      create(:app_user_relationship, app: app, user: user, owner: true)
      rel = create(:app_user_relationship, app: app, owner: false)

      params = {
        attributes: {
          owner: true
        }
      }
      patch app_user_url(app_id: app.id, id: rel.user.id, host: host), params: params.to_json, headers: headers
      rel.reload
      expect(rel.owner).to be true
    end

    it 'creates a relationship if one does not exist yet' do
      app = create(:app)
      create(:app_user_relationship, app: app, user: user, owner: true)
      new_owner = create(:user)

      params = {
        attributes: {
          owner: true
        }
      }

      patch app_user_url(app_id: app.id, id: new_owner.id, host: host), params: params.to_json, headers: headers
      app.reload
      rel = app.app_user_relationships.last
      expect(rel.owner).to be true
      expect(rel.user).to eq new_owner
    end

    it 'does not allow the user to update the owners if he is not an owner or admin' do
      app = create(:app)
      create(:app_user_relationship, app: app, user: user, owner: false)
      new_owner = create(:user)

      params = {
        attributes: {
          owner: true
        }
      }

      patch app_user_url(app_id: app.id, id: new_owner.id, host: host), params: params.to_json, headers: headers

      expect(response).to have_http_status(:forbidden)
    end
  end
end
