require 'rails_helper'

RSpec.describe Assistant::Action::AppCreationHandleService do
  describe '#execute' do
    context 'when the action has a valid message with data model tool part' do
      it 'creates object types with the correct user' do
        user = create(:user)
        chat = create(:assistant_chat, user: user)

        data_model_template = [
          {
            name_en: 'Project',
            icon: 'fa fa-project-diagram',
            color: '#3498db',
            reference: 'project_ref',
            attributes: [
              {
                name_en: 'Name',
                key: 'name',
                data_type: 'String',
                reference: 'project_name_ref'
              },
              {
                name_en: 'Description',
                key: 'description',
                data_type: 'String',
                reference: 'project_description_ref'
              }
            ]
          },
          {
            name_en: 'Task',
            icon: 'fa fa-tasks',
            color: '#e74c3c',
            reference: 'task_ref',
            attributes: [
              {
                name_en: 'Title',
                key: 'title',
                data_type: 'String',
                reference: 'task_title_ref'
              },
              {
                name_en: 'Completed',
                key: 'completed',
                data_type: 'Boolean',
                reference: 'task_completed_ref'
              }
            ]
          }
        ]

        tool_part_content = {
          'type' => 'tool',
          'data' => {
            'tool_name' => 'assistant_tools_suggest_data_model_tool__execute',
            'output' => {
              'data' => {
                'objects' => data_model_template
              }
            }
          }
        }

        message = create(:assistant_message, chat: chat, origin: 'assistant', content: [tool_part_content])
        action = create(:assistant_action, action_type: 'app_creation', message: message)

        result = described_class.execute(action)
        expect(result[:data][:object_type_ids]).to be_present
        expect(result[:data][:object_type_ids].length).to eq 2

        created_object_types = Custom::ObjectType.includes(:user, :users).where(id: result[:data][:object_type_ids])

        created_object_types.each do |object_type|
          expect(object_type.user_id).to eq(user.id)
          expect(object_type.users).to eq([user])
        end
      end
    end
  end
end
