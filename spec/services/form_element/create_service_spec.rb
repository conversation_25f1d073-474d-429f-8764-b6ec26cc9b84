require 'rails_helper'

RSpec.describe FormElement::CreateService do
  describe '#execute' do
    it 'is able to create a Form Element with an attribute' do
      form_tab = create(:form_tab)
      params = ActionController::Parameters.new({
                                                  data: {
                                                    attributes: {
                                                      input_type: 'TextField',
                                                      position: 0,
                                                      locked: false,
                                                      hidden: { '' => false }
                                                    },
                                                    relationships: {
                                                      form_tab: {
                                                        data: {
                                                          id: form_tab.id,
                                                          type: 'form_tab'
                                                        }
                                                      },
                                                      object_type_attribute: {
                                                        data: {
                                                          __guid__: 'local-id',
                                                          type: 'object_type_attribute'
                                                        }
                                                      }
                                                    }
                                                  },
                                                  included: [
                                                    {
                                                      __guid__: 'local-id',
                                                      type: 'object_type_attribute',
                                                      attributes: {
                                                        data_type: 'String',
                                                        name: 'Author',
                                                        key: 'author',
                                                        field_identifier: 'author',
                                                        locked: false,
                                                        hidden: { '1' => true }
                                                      }
                                                    }
                                                  ]
                                                })

      form_element = described_class.execute(params)
      expect(form_element.input_type).to eq 'TextField'
      expect(form_element.locked).to be false
      expect(form_element.hidden['']).to be false
      expect(form_element.object_type_attribute.name).to eq 'Author'
      expect(form_element.object_type_attribute.key).to eq 'author'
      expect(form_element.object_type_attribute.data_type).to eq 'String'
      expect(form_element.object_type_attribute.locked).to be false
      expect(form_element.object_type_attribute.hidden['1']).to be true
      expect(form_element.object_type_attribute.object_type_id).to eq form_tab.object_type_id
    end

    it 'is able to create a Form Element with an attribute that has allowed_values with type "Select" to create SelectableValues' do
      form_tab = create(:form_tab)
      params = ActionController::Parameters.new({
                                                  data: {
                                                    attributes: {
                                                      input_type: 'Radio',
                                                      position: 0
                                                    },
                                                    relationships: {
                                                      form_tab: {
                                                        data: {
                                                          id: form_tab.id,
                                                          type: 'form_tab'
                                                        }
                                                      },
                                                      object_type_attribute: {
                                                        data: {
                                                          __guid__: 'local-id',
                                                          type: 'object_type_attribute'
                                                        }
                                                      }
                                                    }
                                                  },
                                                  included: [
                                                    {
                                                      __guid__: 'local-id',
                                                      type: 'object_type_attribute',
                                                      attributes: {
                                                        data_type: 'Boolean',
                                                        name: 'Author',
                                                        key: 'author',
                                                        field_identifier: 'author',
                                                        allowed_values: {
                                                          type: 'Select',
                                                          values: [{ value: true, text: 'Yeet', position: 0 },
                                                                   { value: false, text: 'Nope', position: 1 }]
                                                        }
                                                      }
                                                    }
                                                  ]
                                                })

      form_element = described_class.execute(params)
      selectable_values = form_element.object_type_attribute.object_type_attribute_selectable_values
      expect(selectable_values.count).to eq(2)
      expected_values = [[true, 'Yeet'], [false, 'Nope']]
      expect(selectable_values.i18n.pluck(:value, :text)).to match_array expected_values
      expect(form_element.object_type_attribute.allowed_values.key?('values')).to be(false)
    end

    it 'is able to create a Form Element without an attribute' do
      form_tab = create(:form_tab)
      params = ActionController::Parameters.new({
                                                  data: {
                                                    attributes: {
                                                      input_type: 'Checkbox',
                                                      position: 0
                                                    },
                                                    relationships: {
                                                      form_tab: {
                                                        data: {
                                                          id: form_tab.id,
                                                          type: 'form_tab'
                                                        }
                                                      }
                                                    }
                                                  }
                                                })

      form_element = described_class.execute(params)
      expect(form_element.object_type_attribute).to be_nil
    end

    it 'is able to create a Form Element which increments position of others' do
      form_tab = create(:form_tab)
      form_element1 = create(:form_element, form_tab: form_tab, position: 0)
      form_element2 = create(:form_element, form_tab: form_tab, position: 1)
      child_tab = create(:form_tab, object_type: form_tab.object_type, parent: form_tab, position: 2)

      params = ActionController::Parameters.new({
                                                  data: {
                                                    attributes: {
                                                      input_type: 'TextField',
                                                      position: 1
                                                    },
                                                    relationships: {
                                                      form_tab: {
                                                        data: {
                                                          id: form_tab.id,
                                                          type: 'form_tab'
                                                        }
                                                      },
                                                      object_type_attribute: {
                                                        data: {
                                                          __guid__: 'local-id',
                                                          type: 'object_type_attribute'
                                                        }
                                                      }
                                                    }
                                                  },
                                                  included: [
                                                    {
                                                      __guid__: 'local-id',
                                                      type: 'object_type_attribute',
                                                      attributes: {
                                                        data_type: 'Number',
                                                        name: 'Count',
                                                        key: 'count',
                                                        field_identifier: 'count'
                                                      }
                                                    }
                                                  ]
                                                })

      form_element = described_class.execute(params)
      expected = [[form_element1.id, 0], [form_element.id, 1], [form_element2.id, 2]]
      expect(form_tab.form_elements.pluck(:id, :position)).to match_array expected
      expect(form_tab.children.pluck(:id, :position)).to contain_exactly([child_tab.id, 3])
    end

    it 'is able to create a Form Element with attachments' do
      form_tab = create(:form_tab)
      blob_a = ActiveStorage::Blob.create!(checksum: 'a', byte_size: 0, filename: 'a')
      blob_b = ActiveStorage::Blob.create!(checksum: 'b', byte_size: 0, filename: 'b')
      blob_c = ActiveStorage::Blob.create!(checksum: 'c', byte_size: 0, filename: 'c')
      params = ActionController::Parameters.new({
                                                  data: {
                                                    attributes: {
                                                      input_type: 'Attachment',
                                                      position: 0
                                                    },
                                                    relationships: {
                                                      form_tab: {
                                                        data: {
                                                          id: form_tab.id,
                                                          type: 'form_tab'
                                                        }
                                                      },
                                                      attachments: {
                                                        data: [
                                                          {
                                                            __guid__: 'abcd',
                                                            type: 'attachment'
                                                          },
                                                          {
                                                            __guid__: 'bcda',
                                                            type: 'attachment'
                                                          },
                                                          {
                                                            __guid__: 'cdab',
                                                            type: 'attachment'
                                                          }
                                                        ]
                                                      }
                                                    }
                                                  },
                                                  included: [
                                                    {
                                                      __guid__: 'abcd',
                                                      type: 'attachment',
                                                      attributes: {
                                                        signed_id: blob_a.signed_id,
                                                        name: 'Good file',
                                                        position: 0
                                                      }
                                                    },
                                                    {
                                                      __guid__: 'bcda',
                                                      type: 'attachment',
                                                      attributes: {
                                                        signed_id: blob_b.signed_id,
                                                        name: 'Better file',
                                                        position: 1
                                                      }
                                                    },
                                                    {
                                                      __guid__: 'cdab',
                                                      type: 'attachment',
                                                      attributes: {
                                                        signed_id: blob_c.signed_id,
                                                        name: 'Best file',
                                                        position: 2
                                                      }
                                                    }
                                                  ]
                                                })

      form_element = described_class.execute(params)
      expect(form_element.input_type).to eq 'Attachment'
      expect(form_element.attachments.length).to eq 3
    end

    it 'is able to create a Form Element with a calculated attribute' do
      form_tab = create(:form_tab)
      relationship = create(:object_type_attribute, :object_relationship, object_type: form_tab.object_type, key: 'labels')
      data_attribute = create(:object_type_attribute, object_type: relationship.target_object_type)
      params = ActionController::Parameters.new(
        {
          data: {
            attributes: {
              input_type: 'TextField',
              position: 0,
              locked: false,
              hidden: { '' => false }
            },
            relationships: {
              form_tab: {
                data: {
                  id: form_tab.id,
                  type: 'form_tab'
                }
              },
              object_type_attribute: {
                data: {
                  __guid__: 'local-id',
                  type: 'object_type_attribute'
                }
              }
            }
          },
          included: [
            {
              __guid__: 'local-id',
              type: 'object_type_attribute',
              attributes: {
                data_type: 'String',
                name: 'Lookup',
                key: 'lookup',
                field_identifier: 'lookup',
                locked: false,
                calculated: true
              }, relationships: {
                calculation: {
                  data: {
                    type: 'calculation', __guid__: 'guid'
                  }
                }
              }
            }, {
              type: 'calculation',
              __guid__: 'guid',
              attributes: {
                calculation_type: 'lookup'
              },
              relationships: {
                object_type_attribute_paths: {
                  data: [{
                    type: 'object_type_attribute_path', __guid__: 'path1'
                  }]
                }
              }
            }, {
              type: 'object_type_attribute_path',
              __guid__: 'path1',
              attributes: {
                relationship_references: [
                  { attribute_id: relationship.id, inverse: false }
                ]
              },
              relationships: {
                data_attribute: {
                  data: { type: 'object_type_attribute', id: data_attribute.id }
                }
              }
            }
          ]
        }
      )

      allow(CalculationInitializeValuesWorker).to receive(:perform_async)
      allow(Calculation::RecalculateWorker).to receive(:perform_async)

      form_element = described_class.execute(params)
      expect(form_element.input_type).to eq 'TextField'
      expect(form_element.locked).to be false
      expect(form_element.hidden['']).to be false

      attribute = form_element.object_type_attribute
      expect(attribute.name).to eq 'Lookup'
      expect(attribute.key).to eq 'lookup'
      expect(attribute.data_type).to eq 'String'
      expect(attribute.__guid__).to eq 'local-id'
      expect(attribute.object_type_id).to eq form_tab.object_type_id
      expect(CalculationInitializeValuesWorker).to have_received(:perform_async).with(attribute.calculation.id)
      expect(Calculation::RecalculateWorker).not_to have_received(:perform_async)
      expect(attribute.calculated).to be true
      expect(attribute.calculation).to be_present
      expect(attribute.calculation.__guid__).to eq 'guid'
      expect(attribute.calculation.calculation_type).to eq 'lookup'
      expect(attribute.calculation.object_type_attribute_paths.length).to eq 1
      path = attribute.calculation.object_type_attribute_paths.first
      expect(path.data_attribute).to eq data_attribute
      expect(path.__guid__).to eq 'path1'
      expect(path.relationship_references).to contain_exactly(
        { 'attribute_id' => relationship.id, 'inverse' => false }
      )
    end

    it 'is will calculate the field for all object records if the Form Element has a formula based calculated attribute' do
      form_tab = create(:form_tab)
      relationship = create(:object_type_attribute, :object_relationship, object_type: form_tab.object_type, key: 'labels')
      data_attribute = create(:object_type_attribute, object_type: relationship.target_object_type)
      params = ActionController::Parameters.new(
        {
          data: {
            attributes: {
              input_type: 'TextField',
              position: 0,
              locked: false,
              hidden: { '' => false }
            },
            relationships: {
              form_tab: {
                data: {
                  id: form_tab.id,
                  type: 'form_tab'
                }
              },
              object_type_attribute: {
                data: {
                  __guid__: 'local-id',
                  type: 'object_type_attribute'
                }
              }
            }
          },
          included: [
            {
              __guid__: 'local-id',
              type: 'object_type_attribute',
              attributes: {
                data_type: 'String',
                name: 'Lookup',
                key: 'lookup',
                field_identifier: 'lookup',
                locked: false,
                calculated: true
              }, relationships: {
                calculation: {
                  data: {
                    type: 'calculation', __guid__: 'guid'
                  }
                }
              }
            }, {
              type: 'calculation',
              __guid__: 'guid',
              attributes: {
                calculation_type: 'formula',
                formula: '2 * A'
              },
              relationships: {
                object_type_attribute_paths: {
                  data: [{
                    type: 'object_type_attribute_path', __guid__: 'path1'
                  }]
                }
              }
            }, {
              type: 'object_type_attribute_path',
              __guid__: 'path1',
              attributes: {
                relationship_references: [
                  { attribute_id: relationship.id, inverse: false }
                ]
              },
              relationships: {
                data_attribute: {
                  data: { type: 'object_type_attribute', id: data_attribute.id }
                }
              }
            }
          ]
        }
      )

      allow(CalculationInitializeValuesWorker).to receive(:perform_async)
      allow(Calculation::RecalculateWorker).to receive(:perform_async)

      form_element = described_class.execute(params)
      expect(CalculationInitializeValuesWorker).not_to have_received(:perform_async)
      expect(Calculation::RecalculateWorker).to have_received(:perform_async).with(form_element.object_type_attribute.calculation.id)
    end
  end
end
