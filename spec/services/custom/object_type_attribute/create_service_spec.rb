require 'rails_helper'

RSpec.describe Custom::ObjectTypeAttribute::CreateService do
  describe '#execute' do
    let(:object_type) { create(:object_type) }

    it 'creates an object type attribute' do
      params = {
        attributes: {
          name_en: 'Incident type',
          key: 'incident_type',
          field_identifier: 'incident_type',
          data_type: 'String',
          locked: false,
          hidden: { '2' => true }
        }
      }
      params = ActionController::Parameters.new(params)
      object_type_attribute = described_class.execute(params, object_type: object_type)
      expect(object_type_attribute.key).to eq 'incident_type'
      expect(object_type_attribute.locked).to be false
      expect(object_type_attribute.hidden['2']).to be true
    end

    it 'creates an object type attribute with selectable values' do
      values = [
        { text: 'Human error', value: 'human_error' },
        { text: 'Machine failure', value: 'machine_failure' }
      ]
      params = ActionController::Parameters.new({ attributes: { name_en: 'Incident type', key: 'incident_type',
                                                                field_identifier: 'incident_type',
                                                                data_type: 'String', allowed_values: {
                                                                  values: values, type: 'Select'
                                                                } } })
      object_type_attribute = described_class.execute(params, object_type: object_type)
      allowed_values = object_type_attribute.object_type_attribute_selectable_values

      expect(allowed_values.count).to eq 2
      expect(allowed_values.pluck(:value)).to eq(%w[human_error machine_failure])
      expect(object_type_attribute.allowed_values).to eq({ 'type' => 'Select' })
    end

    it 'does not create an archived attribute' do
      values = [
        { text: 'Machine exploded', value: 'machine_failure' },
        { text: 'Human error', value: 'human_error' },
        { text: 'Robot error', value: 'robot_error', archived: true }
      ]

      params = ActionController::Parameters.new({ attributes: { name_en: 'Incident type', key: 'incident_type',
                                                                field_identifier: 'incident_type', data_type: 'String',
                                                                allowed_values: { values: values, type: 'Select' } } })

      object_type_attribute = described_class.execute(params, object_type: object_type)
      allowed_values = object_type_attribute.object_type_attribute_selectable_values

      expect(allowed_values.count).to eq 2
      expected_values = [
        ['Machine exploded', 0, 'machine_failure'],
        ['Human error', 1, 'human_error']
      ]

      expect(allowed_values.i18n.pluck(:text, :position, :value)).to eq(expected_values)
      expect(object_type_attribute.allowed_values).to eq({ 'type' => 'Select' })
    end

    it 'creates an object type attribute with unique selectable values' do
      values = [
        { text: 'Human error', value: 'human_error' },
        { text: 'Machine failure', value: 'machine_failure' },
        { text: 'Human mistake', value: 'human_error' }
      ]
      params = ActionController::Parameters.new({ attributes: { name_en: 'Incident type', key: 'incident_type',
                                                                field_identifier: 'incident_type', data_type: 'String',
                                                                allowed_values: { values: values, type: 'Select' } } })
      object_type_attribute = described_class.execute(params, object_type: object_type)
      allowed_values = object_type_attribute.object_type_attribute_selectable_values

      expect(allowed_values.count).to eq 2
      expect(allowed_values.pluck(:value)).to eq(%w[human_error machine_failure])
      expect(allowed_values.i18n.pluck(:text)).to eq(['Human mistake', 'Machine failure'])
      expect(object_type_attribute.allowed_values).to eq({ 'type' => 'Select' })
    end

    it 'creates new selectable values' do
      I18n.with_locale(:nl) do
        values = [
          { text: 'Machine exploded', value: 'machine_failure' }
        ]

        params = ActionController::Parameters.new({ attributes: { name_en: 'Incident type', data_type: 'String', key: 'incident_type',
                                                                  field_identifier: 'incident_type',
                                                                  allowed_values: { values: values, type: 'Select' } } })
        object_type_attribute = described_class.execute(params, object_type: object_type)
        allowed_value = object_type_attribute.object_type_attribute_selectable_values.take

        expect(allowed_value.text).to eq('Machine exploded')
      end
    end

    it 'creates an object_type_attribute with inverse relationships' do
      other_ot = create(:object_type)
      linked_ota = create(:object_type_attribute, :object_relationship, object_type: other_ot, name: 'Labels', key: 'labels',
                                                                        target_object_type: object_type)
      invalid_linked_ota = create(:object_type_attribute, :object_relationship, object_type: other_ot, name: 'invalid', key: 'invalid',
                                                                                target_object_type: other_ot, inverse: true)

      params = ActionController::Parameters.new({ attributes: { name_en: 'Incident type', key: 'incident_type',
                                                                field_identifier: 'incident_type',
                                                                data_type: 'Relationship', relationship_kind: 'single',
                                                                inverse_relationship_kind: 'multiple',
                                                                target_class: 'Object', inverse: true,
                                                                target_object_type: other_ot, target_column_name: 'id' },
                                                  relationships: { linked_object_type_attributes: { data: [{
                                                    type: 'object_type_attribute', id: linked_ota.id
                                                  }, { type: 'object_type_attribute', id: invalid_linked_ota.id }] } } })
      object_type_attribute = described_class.execute(params, object_type: object_type)
      linked_otas = object_type_attribute.linked_object_type_attributes

      expect(linked_otas.first).to eq linked_ota
      expect(linked_otas).not_to include invalid_linked_ota
    end

    it 'does not create and object_type_attribute with inverse relationships when inverse is set to false' do
      other_ot = create(:object_type)
      linked_ota = create(:object_type_attribute, :object_relationship, object_type: other_ot, name: 'Labels', key: 'labels',
                                                                        target_object_type: object_type)

      params = ActionController::Parameters.new({ attributes: { name_en: 'Incident type', key: 'incident_type',
                                                                field_identifier: 'incident_type',
                                                                data_type: 'Relationship', relationship_kind: 'single',
                                                                inverse_relationship_kind: 'multiple',
                                                                target_class: 'Object', inverse: false,
                                                                target_object_type: other_ot, target_column_name: 'id' },
                                                  relationships: { linked_object_type_attributes: { data: [{
                                                    type: 'object_type_attribute', id: linked_ota.id
                                                  }] } } })
      object_type_attribute = described_class.execute(params, object_type: object_type)
      linked_otas = object_type_attribute.linked_object_type_attributes

      expect(linked_otas.count).to eq 0
    end

    it 'creates with default value' do
      target_ot = create(:object_type)
      target_ota = create(:object_type_attribute, :object_relationship, object_type: target_ot, name: 'Labels', key: 'labels')

      target_object_ids = create_list(:object, 2, object_type: target_ot).map { |object| object.id.to_s }

      params = ActionController::Parameters.new({
                                                  attributes: {
                                                    name_en: 'Machine labels',
                                                    key: 'machine_labels',
                                                    field_identifier: 'machine_labels',
                                                    data_type: 'Relationship',
                                                    relationship_kind: 'multiple',
                                                    inverse_relationship_kind: 'multiple',
                                                    target_class: 'Object',
                                                    inverse: false,
                                                    target_object_type: target_ot,
                                                    target_column_name: 'id',
                                                    default_value: [{
                                                      value: target_object_ids,
                                                      dynamic: false
                                                    }]
                                                  },
                                                  relationships: {
                                                    linked_object_type_attributes: {
                                                      data: [{
                                                        type: 'object_type_attribute', id: target_ota.id.to_s
                                                      }]
                                                    }
                                                  }
                                                })

      object_type_attribute = described_class.execute(params, object_type: object_type)
      expect(object_type_attribute.default_value.first['value']).to match_array target_object_ids
    end

    it 'creates with lookup calculation' do
      relationship = create(:object_type_attribute, :object_relationship, object_type: object_type, name: 'Labels', key: 'labels')
      data_attribute = create(:object_type_attribute, object_type: relationship.target_object_type)

      params = ActionController::Parameters.new(
        {
          attributes: {
            name: 'Lookup',
            key: 'lookup',
            field_identifier: 'lookup',
            data_type: 'String',
            calculated: true
          },
          relationships: {
            calculation: {
              data: {
                type: 'calculation', __guid__: 'guid'
              }
            }
          }, included: [{
            type: 'calculation',
            __guid__: 'guid',
            attributes: {
              calculation_type: 'lookup'
            },
            relationships: {
              object_type_attribute_paths: {
                data: [
                  { type: 'object_type_attribute_path', __guid__: 'path1' }
                ]
              }
            }
          }, {
            type: 'object_type_attribute_path',
            __guid__: 'path1',
            attributes: {
              relationship_references: [{ attribute_id: relationship.id, inverse: false }]
            },
            relationships: {
              data_attribute: {
                data: { type: 'object_type_attribute', id: data_attribute.id }
              }
            }
          }]
        }
      )

      # NOTE: Enable these after using after_commit transaction scheduling hooks
      # allow(CalculationInitializeValuesWorker).to receive(:perform_async)
      object_type_attribute = described_class.execute(params, object_type: object_type)
      expect(object_type_attribute.calculation).to be_present
      expect(object_type_attribute.calculation.__guid__).to eq 'guid'
      expect(object_type_attribute.calculation.calculation_type).to eq 'lookup'
      expect(object_type_attribute.calculation.object_type_attribute_paths.length).to eq 1
      path = object_type_attribute.calculation.object_type_attribute_paths.first
      expect(path.data_attribute).to eq data_attribute
      expect(path.__guid__).to eq 'path1'
      expect(path.relationship_references).to contain_exactly({ 'attribute_id' => relationship.id, 'inverse' => false })
      # expect(CalculationInitializeValuesWorker).to have_received(:perform_async).with(object_type_attribute.calculation.id)
    end

    it 'creates with formula calculation' do
      data_attribute = create(:object_type_attribute, object_type: object_type)
      mapping = {
        'A' => 1,
        'B' => 2
      }

      params = ActionController::Parameters.new(
        {
          attributes: {
            name: 'Formula',
            key: 'Formula',
            field_identifier: 'formula',
            data_type: 'String',
            calculated: true
          },
          relationships: {
            calculation: {
              data: {
                type: 'calculation', __guid__: 'guid'
              }
            }
          }, included: [{
            type: 'calculation',
            __guid__: 'guid',
            attributes: {
              calculation_type: 'formula',
              formula: 'A + B'
            },
            relationships: {
              object_type_attribute_paths: {
                data: [
                  { type: 'object_type_attribute_path', __guid__: 'path1' },
                  { type: 'object_type_attribute_path', __guid__: 'path2' }
                ]
              }
            }
          }, {
            type: 'object_type_attribute_path',
            __guid__: 'path1',
            attributes: {
              relationship_references: [],
              key: 'A'
            },
            relationships: {
              data_attribute: {
                data: { type: 'object_type_attribute', id: data_attribute.id }
              }
            }
          }, {
            type: 'object_type_attribute_path',
            __guid__: 'path2',
            attributes: {
              relationship_references: [],
              key: 'B',
              column_name: 'id',
              mapping: mapping
            }
          }]
        }
      )

      # NOTE: Enable these after using after_commit transaction scheduling hooks
      # allow(CalculationInitializeValuesWorker).to receive(:perform_async)
      object_type_attribute = described_class.execute(params, object_type: object_type)
      expect(object_type_attribute.calculation).to be_present
      expect(object_type_attribute.calculation.__guid__).to eq 'guid'
      expect(object_type_attribute.calculation.calculation_type).to eq 'formula'
      expect(object_type_attribute.calculation.object_type_attribute_paths.length).to eq 2
      path1 = object_type_attribute.calculation.object_type_attribute_paths.first
      expect(path1.data_attribute).to eq data_attribute
      expect(path1.column_name).to be_nil
      expect(path1.__guid__).to eq 'path1'
      expect(path1.key).to eq 'A'
      path2 = object_type_attribute.calculation.object_type_attribute_paths.second
      expect(path2.column_name).to eq 'id'
      expect(path2.data_attribute).to be_nil
      expect(path2.__guid__).to eq 'path2'
      expect(path2.key).to eq 'B'
      expect(path2.mapping).to eq mapping
      # expect(CalculationInitializeValuesWorker).not_to have_received(:perform_async)
    end

    it 'creates with calculated true but no calculation' do
      params = ActionController::Parameters.new(
        {
          attributes: {
            name: 'Lookup',
            key: 'lookup',
            field_identifier: 'lookup',
            data_type: 'String',
            calculated: true
          },
          relationships: {
            calculation: {}
          }, included: []
        }
      )

      object_type_attribute = described_class.execute(params, object_type: object_type)
      expect(object_type_attribute.calculation).not_to be_present
      expect(object_type_attribute.calculated).to be true
    end

    context 'when OTAs number is around Custom::ObjectType::OBJECT_TYPE_ATTRIBUTES_LIMIT' do
      before { stub_const 'Custom::ObjectType::OBJECT_TYPE_ATTRIBUTES_LIMIT', 4 }

      it 'raises ObjectType::ObjectTypeAttributesLimitError when OT ends up having more than the OTA limit' do
        params = {
          attributes: {
            name_en: 'Incident type',
            key: 'incident_type',
            data_type: 'String',
            locked: false,
            hidden: { '2' => true }
          }
        }

        params = ActionController::Parameters.new(params)
        create_list(:object_type_attribute, 4, object_type: object_type, archived: false)
        expect { described_class.execute(params, object_type: object_type) }.to raise_error(ObjectType::ObjectTypeAttributesLimitError)
      end

      it 'does not raise ObjectType::ObjectTypeAttributesLimitError when OT has fewer unarchived OTAs than the limit' do
        params = {
          attributes: {
            name_en: 'Incident type',
            key: 'incident_type',
            data_type: 'String',
            locked: false,
            hidden: { '2' => true },
            field_identifier: 'field'
          }
        }

        params = ActionController::Parameters.new(params)
        create_list(:object_type_attribute, 4, object_type: object_type, archived: true)
        create_list(:object_type_attribute, 3, object_type: object_type, archived: false)
        expect { described_class.execute(params, object_type: object_type) }.not_to raise_error
      end
    end
  end
end
