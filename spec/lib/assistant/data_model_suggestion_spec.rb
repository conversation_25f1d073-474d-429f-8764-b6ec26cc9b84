require 'rails_helper'

RSpec.describe 'Assistant::DataModelSuggestion' do
  describe 'tool schemas' do
    it 'for data model are in sync with the DataModelTemplate schemas', skip: 'needs better spec' do
      model_template = [
        {
          'reference' => 'incident',
          'name_en' => 'Incident',
          'name_fr' => 'Problem',
          'name_nl' => 'Incident',
          'icon' => 'fa fa-triangle-exclamation',
          'color' => '#E1383C',
          'attributes' => [
            {
              'reference' => 'incident_author',
              'name_en' => 'Author',
              'name_fr' => 'Auteur',
              'name_nl' => 'Auteur',
              'data_type' => 'String',
              'description' => 'The name of the author of the incident.'
            },
            {
              'reference' => 'owner',
              'name_en' => 'Owner',
              'name_fr' => 'Propriétaire',
              'name_nl' => 'Eigenaar',
              'data_type' => 'Relationship',
              'relationship_kind' => 'single',
              'inverse_relationship_kind' => 'multiple',
              'target_class' => 'User'
            },
            {
              'reference' => 'incident_category',
              'name_en' => 'Main Category',
              'name_fr' => 'Categorie Principal',
              'data_type' => 'Relationship',
              'relationship_kind' => 'single',
              'inverse_relationship_kind' => 'multiple',
              'target_class' => 'Object',
              'target_object_type_reference' => 'category',
              'target_object_type_attribute_reference' => 'category_name'
            },
            {
              'reference' => 'incident_actions',
              'name_en' => 'Actions',
              'name_fr' => 'Actions',
              'data_type' => 'Relationship',
              'relationship_kind' => 'multiple',
              'inverse_relationship_kind' => 'single',
              'target_class' => 'Object',
              'target_object_type_reference' => 'action',
              'target_object_type_attribute_reference' => 'action_name'
            },
            {
              'reference' => 'incident_status',
              'name_en' => 'Status',
              'name_fr' => 'Statut',
              'data_type' => 'String',
              'allowed_values' => { 'type' => 'Select',
                                    'values' => [{ 'value' => 'open', 'text_en' => 'Open' },
                                                 { 'value' => 'closed', 'text_en' => 'Closed' }] }
            },
            {
              'reference' => 'incident_notes',
              'name_en' => 'Readonly notes',
              'name_fr' => 'Notes',
              'data_type' => 'HTML'
            },
            {
              'reference' => 'incident_opened_date',
              'name_en' => 'Opened date',
              'name_fr' => 'Date',
              'data_type' => 'Date'
            },
            {
              'reference' => 'incident_team',
              'name_en' => 'Team',
              'name_fr' => 'Equipe',
              'data_type' => 'Relationship',
              'target_class' => 'User',
              'relationship_kind' => 'multiple',
              'inverse_relationship_kind' => 'multiple'
            },
            {
              'reference' => 'incident_attachment',
              'name_en' => 'Attachment',
              'name_fr' => 'Attachement',
              'data_type' => 'Attachment'
            }
          ]
        },
        {
          'id' => 1,
          'reference' => 'action',
          'name_en' => 'Action',
          'name_fr' => 'Action',
          'name_nl' => 'Actie',
          'icon' => 'fa fa-wrench',
          'color' => '#E1383C',
          'attributes' => [
            {
              'id' => 1,
              'reference' => 'action_name',
              'name_en' => 'Name',
              'name_fr' => 'Nom',
              'name_nl' => 'Naam',
              'data_type' => 'String'
            }
          ]
        },
        {
          'id' => 2,
          'reference' => 'category',
          'name_en' => 'Action',
          'name_fr' => 'Action',
          'icon' => 'fa fa-list',
          'color' => '#E1383C',
          'attributes' => [
            {
              'id' => 2,
              'reference' => 'category_name',
              'name_en' => 'Name',
              'name_fr' => 'Name',
              'data_type' => 'String'
            }
          ]
        }
      ]

      tool_schema = Assistant::Schemas.get_schema('suggest_data_model_schema')
                                      .fetch('properties')
                                      .fetch('objects')
      template_schema = DataModelTemplate::Schemas.get_schema('data_model_schema')

      expect_no_errors(tool_schema.validate(model_template))
      expect_no_errors(template_schema.validate(model_template))
    end

    it 'for forms are in sync with the DataModelTemplate schemas', skip: 'better spec needed' do
      form_template = [
        {
          'object_type_reference' => 'incident',
          'tabs' => [
            { 'name_en' => 'Fields',
              'elements' => [
                { 'input_type' => 'TextField', 'attribute_reference' => 'incident_author', 'text' => 'Author' },
                { 'input_type' => 'Select', 'attribute_reference' => 'incident_status', 'text' => 'Status' },
                { 'input_type' => 'RichText', 'attribute_reference' => 'incident_notes', 'text' => 'can only be edited on creation' },
                { 'name_en' => 'section',
                  'elements' => [
                    { 'input_type' => 'Date', 'attribute_reference' => 'incident_opened_date', 'text' => 'Opening date' }
                  ] },
                { 'input_type' => 'Description', 'text' => 'This is just text', 'attribute_reference' => 'null' }
              ] },
            { 'name_en' => 'tab 2 - relationships',
              'elements' => [
                { 'input_type' => 'Relationship', 'attribute_reference' => 'incident_category', 'text' => 'Main category' },
                { 'input_type' => 'Relationship', 'attribute_reference' => 'incident_team' },
                { 'input_type' => 'Attachment', 'attribute_reference' => 'incident_attachment' }
              ] }
          ]
        }
      ]

      tool_schema = Assistant::Schemas.get_schema('suggest_forms_schema')
                                      .fetch('properties')
                                      .fetch('forms')
      template_schema = DataModelTemplate::Schemas.get_schema('forms_schema')

      expect_no_errors(tool_schema.validate(form_template))
      expect_no_errors(template_schema.validate(form_template))
    end
  end

  private

  def expect_no_errors(enum)
    errors = enum.to_a
    expect(errors).to be_empty, JSON.pretty_generate(errors)
  end
end
