stack_paths:
  # Array of regular expressions describing N+1 stack reports to ignore
  - objects_controller.*create
  - objects_controller.*update
  - app/models/custom/object.*set_and_update_local_id
  # TODO: re-evaluate this ignore once !1832 is merged
  - object/update_service
  - services/custom/object/auto_calculation_service
  - services/calculation/formula_evaluate_service
  - app/queries/object_type_attribute_path/object_value_query
  - data_source_query/post_processing
  - custom/object_type/create_or_update_from_template_service
  - mailers/object_mailer
  - form_tabs_controller.*destroy
  - form_tabs_controller.*clone
  - interface_elements_controller.*clone
  - object_types_controller.*destroy
  - object_type_attribute/destroy_service.*
  - automations_controller.*destroy
  - services/custom/object/update_service.*assign_relationships
  - object_query_builder/builder
  - form_element/create_service.*assign_attachments
  - form_element/update_service.*assign_attachments
  # Mobility makes some uncontrollable N+1 queries when destroying translations
  - mobility.*define_after_destroy_callback
  # AwesomeNestedSet makes some nested queries which are not under our control
  - awesome_nested_set.*set_default_left_and_right
  - awesome_nested_set.*move_to
  - object_type_description/update_service.*assign_attachments
  - services/assistant/action/app_creation_handle_service
  - services/form_element/create_service
  - services/form_element/update_service
  - services/object_type_description/create_service
  - services/object_type_description/update_service
  - services/form_tab/destroy_service
  - services/form_tab/clone_service
  - services/custom/object_type/clone_service
  - services/thumbnail/clone_service
  - services/interface_element/update_service.*update_attachments
  - services/interface_element/update_service.*update_object_query_view
  - services/interface/element/attachment/clone_service.*clone_attachments
  - services/interface/element/link/update_service.*shortcut_target
  - lib/json_backup/base.*deserialize
  - lib/data_model_template/
  - interfaces_controller.*destroy
  - interface_elements_controller.*destroy
  - app/models/team.rb.*implicit_admins
  - app/workers/tero_catering_cleanup_worker
  - services/view/update_service.*update_object_query_view
  - services/view/create_service.*create_object_query_view
  - sections_controller.*destroy
