Rails.application.routes.draw do
  # For details on the DSL available within this file, see https://guides.rubyonrails.org/routing.html

  get '/signin', to: 'pages#signin', as: :signin_page
  get '/signup', to: 'users#new', as: :signup_page

  post '/users', to: 'users#create', as: :user_signup

  get '/qr/:code', to: redirect('/#/qr_code/%{code}'), code: /[^\/]+/, as: 'qr'
  resources :sessions, only: [:create]
  delete '/sessions/signout', to: 'sessions#destroy'

  match '/auth/:provider/callback', to: 'sessions#create', via: [:get, :post]
  get '/auth/failure', to: redirect('/signin')

  get '/reset_password', to: 'password_resets#new', as: :reset_password

  resources :password_resets, only: [:create, :show, :new, :update]
  resources :verify_emails, only: [:show]

  get '/data_sources/:uuid', to: 'data_sources/data#index', as: :data_source_data

  scope :api do
    resource :instance, only: [], controller: :instance do
      get :logo
      get :background
    end
    resource :current_instance, only: [:show], controller: :current_instance
    resource :current_user, only: [:show], controller: :current_user

    resources :announcements, only: [:index]
    resources :seen_announcements, only: [:create]
    resources :qr_codes, only: [:show, :create], param: :code, code: /[^\/]+/ do
      member do
        get :file
      end
    end

    resources :interface_elements, only: [:create, :update, :destroy, :show] do
      member do
        post :clone
      end
    end
    resources :interface_team_relationships, only: [:destroy, :update]
    resources :jobs, only: [:show, :index]

    resources :interfaces, only: [:index, :show, :update, :destroy, :create] do
      resources :interface_team_relationships, module: :interfaces, only: [:index]
      resources :users, module: :interfaces, only: [:index, :update]
      member do
        post :share
        put :update_grid
      end
    end

    resources :sections, only: [:create, :update, :destroy] do
      member do
        put :move
      end
    end

    resources :views, only: [:index, :create, :update, :destroy, :show] do
      member do
        get :data
      end
    end
    resources :visualisations, only: [:index, :show, :update]
    resources :object_query_rules, only: [:create, :update, :destroy]
    resources :object_query_views, only: [:index, :create, :update, :destroy]
    resources :object_type_groups, only: [:index]
    resources :object_type_user_relationships, only: [:create]
    resources :object_type_attributes, only: [:index, :show]
    resources :object_type_attribute_validations, only: [:create, :update, :show, :destroy]
    resources :object_type_roles, only: [:index, :create, :update, :show, :destroy]
    resources :object_types, only: [:index, :show, :update, :create, :destroy] do
      member do
        post :preview_calculate
        post :share
        post :publish
      end

      resources :object_type_team_relationships, module: :object_types, only: [:index]
      resources :object_type_variants, module: :object_types, only: [:index]
      resources :teams, module: :object_types, only: [:index]
      resources :users, module: :object_types, only: [:index, :update]
    end
    resources :objects, only: [:index, :show, :update, :create, :destroy] do
      collection do
        post :export
      end

      member do
        get :related_objects
      end

      resources :qr_codes, module: :objects, only: [:index]
      resources :snapshots, module: :objects, only: [:index]
    end
    resources :form_elements, only: [:create, :show, :update, :destroy] do
      member do
        put :move
      end
    end
    resources :form_tabs, only: [:index, :create, :show, :update, :destroy] do
      member do
        put :move
        post :clone
      end
    end
    resources :thumbnails, only: [:index, :update, :create, :destroy]
    resources :shortcuts, only: [:index, :create, :destroy, :update]
    resources :conditional_displays, only: [:show, :create, :update, :destroy]

    resources :teams, only: [:index, :show, :update, :create] do
      resources :interface_team_relationships, module: :teams, only: [:index]
      resources :invitations, module: :teams, only: [:index]
      resources :object_type_team_relationships, module: :teams, only: [:index]
      resources :object_types, module: :teams, only: [:index]
      resources :users, module: :teams, only: [:index, :create, :destroy, :update]
      resources :user_team_join_requests, module: :teams, only: [:index]
      resource :interface, module: :teams, only: [:show]
    end

    resources :users, only: [:index, :show, :update, :destroy] do
      member do
        put :disable
        put :enable
      end
    end

    resources :automations, only: [:create, :update, :destroy, :index, :show] do
      member do
        post :trigger
      end
    end

    resources :automation_actions, only: [:create, :update, :destroy, :show]

    resources :data_sources, only: [:create, :show, :destroy]
    resources :comments, only: [:index, :show, :create, :update, :destroy]
    resources :invitations, only: [:index, :create, :destroy]
    resources :user_team_join_requests, only: [:create, :destroy] do
      member do
        post :accept
        delete :reject
      end
    end
    resources :object_type_descriptions, only: [:index, :create, :update]
    resources :object_type_team_relationships, only: [:update, :destroy]

    resources :calculations, only: [] do
      member do
        post :recalculate
      end

      collection do
        post :validate_formula
      end
    end

    resources :apps, only: [:show, :index, :update] do
      resources :users, module: :apps, only: [:update, :index]
    end

    namespace :admin do
      resources :users, only: [:index]
    end

    namespace :assistant do
      resources :chats, only: [:create]
      resources :messages, only: [:create, :show]
      resources :actions, only: [:create]
    end

    post 'incoming_webhooks/:id', to: 'incoming_webhooks#handle_external_event', as: 'incoming_webhooks'

    get '*type/:type_id/translations/keys/:id', to: 'translations/keys#show'
    put '*type/:type_id/translations/keys/:id', to: 'translations/keys#update'
  end

  direct :frontend do
    '/'
  end
end
