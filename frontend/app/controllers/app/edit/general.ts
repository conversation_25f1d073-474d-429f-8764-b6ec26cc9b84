import Store from "@ember-data/store";
import Controller from "@ember/controller";
import { action } from "@ember/object";
import RouterService from "@ember/routing/router-service";
import Transition from "@ember/routing/transition";
import { service } from "@ember/service";
import { tracked } from "@glimmer/tracking";
import { IntlService } from "ember-intl";
import isBlank from "frontend/helpers/is-blank";
import AppModel from "frontend/models/app";
import User from "frontend/models/user";
import CurrentUserService from "frontend/services/current-user";
import DialogService from "frontend/services/dialog";
import { DEFAULT_SWATCHES } from "frontend/utils/color/swatches";
import { handleError } from "frontend/utils/handle-error";
import { Owners } from "frontend/utils/owners";
import { abortTransition, hasDirtyChecksEnabled, ignoreDirtyChecks } from "frontend/utils/transition";

export default class AppEditGeneralController extends Controller {
  declare model: AppModel;

  declare _warnBeforeUnload: () => void;

  declare _onrouteChange: (transition: Transition) => void;

  @tracked
  declare owners: Owners;

  @service
  declare store: Store;

  @service
  declare router: RouterService;

  @service
  declare intl: IntlService;

  @service
  declare dialog: DialogService;

  @service
  declare currentUser: CurrentUserService;

  get defaultColors() {
    return DEFAULT_SWATCHES;
  }

  get disableSaveButton() {
    return isBlank(this.model.name) || this.model.name.length > 255;
  }

  @action
  setInputAppProperty(key: 'name' | 'shortDescription', event: GenericEvent<HTMLInputElement>) {
    this.setAppProperty(key, event.target.value);
  }

  @action
  setAppProperty<T extends keyof AppModel>(key: T, value: AppModel[T]) {
    this.model[key] = value;
    this.model.isDirty = true;
  }

  @action
  setAppColor(color?: string) {
    this.setAppProperty('color', color || null);
  }

  @action
  async saveApp() {
    try {
      if (!(await this.owners.confirmRemovingCurrentUser(this.currentUser.user, this.dialog, this.intl.t("dialog.confirm_removal_current_user_as_app_owner")))) return;

      if (!this.owners.selected.length) {
        handleError(this, undefined, { text: this.intl.t('error.empty_app_owners_list') });
        return;
      }

      await Promise.all([
        this.model.save(),
        this.owners.newIds.map((ownerId) => this.store.adapterFor('app-users').updateAppUser(this.model.id, ownerId, true)),
        this.owners.removedIds.filter((ownerId) => ownerId !== this.currentUser.user.id).map((ownerId) => this.store.adapterFor('app-users').updateAppUser(this.model.id, ownerId, false))
      ]);

      this.model.isDirty = false;
      if (!this.owners.selected.some((owner) => owner.id == this.currentUser.user.id)) {
        this.currentUser.user.ownedApps.removeObject(this.model.id);
        // Transition to index page when removing yourself when you're not a global admin
        if (!this.currentUser.user.admin) this.router.transitionTo("apps");
      }
      this.owners.initial = [...this.owners.selected];
    } catch (e) {
      handleError(this, e);
    }
  }

  @action
  setSelectedOwners(users: Array<User>) {
    this.owners.selected = users;
    this.model.isDirty = true;
  }

  registerDirtyWarnings() {
    this._warnBeforeUnload = this.warnBeforeUnload.bind(this);
    window.addEventListener('beforeunload', this._warnBeforeUnload);

    this._onrouteChange = this.onrouteChange.bind(this);
    this.router.on('routeWillChange', this._onrouteChange);
  }

  unregisterDirtyWarnings() {
    window.removeEventListener('beforeunload', this._warnBeforeUnload);
    this.router.off('routeWillChange', this._onrouteChange);
  }

  async onrouteChange(transition: Transition) {
    if (transition.isAborted) return;

    if (hasDirtyChecksEnabled(transition) && this.model.isDirty) {
      abortTransition(transition, this.router);

      const result = await this.openDirtyDialog();

      if (result) {
        ignoreDirtyChecks(transition);
        transition.retry();
      }
    }
  }

  warnBeforeUnload(event: BeforeUnloadEvent) {
    if (!this.model.isDirty) return;
    event.preventDefault();
    // setting returnValue to a value other than null or undefined will prompt the dialog
    // In older browsers, the returnValue of the event is displayed in this dialog.
    return event.returnValue = this.intl.t("dialog.confirm_unsaved_changes_app");
  }

  async openDirtyDialog() {
    const result = await this.dialog.confirm(this.intl.t("dialog.confirm_unsaved_changes_app"));

    if (result) {
      this.model.rollbackAttributes();
      this.model.isDirty = false;
    }

    return result;
  }
}
