{{page-title this.model.name}}

<div class="app-show-page mb-4">
  <div class="m-2 d-flex align-items-center">
    <LinkTo @route="apps" class="btn d-lg-none border-0"><i class="fa fa-arrow-left fa-lg"></i></LinkTo>
    <span class="fa-stack fs-90 flex-shrink-0">
      <i class="fa fa-hexagon fa-stack-2x {{unless this.model.color "text-primary"}}" style={{color this.model.color}} />
      <i class="{{this.model.icon}} fa-stack-1x text-white" />
    </span>
    <button type="button" data-bs-toggle="dropdown" class="btn rounded ps-2 standard-fontsize border-0" data-bs-offset="0,0">
      <span class="fw-bold">{{this.model.name}}</span>
      <i class="fa fa-caret-down"></i>
    </button>
    <div class="dropdown-menu">
      <div class="scrollable-dropdown">
        {{#each this.viewpointApps as |app|}}
          <LinkTo @route="app.show" @model={{app.id}} class="d-flex align-items-center dropdown-item" @activeClass="">
            <span class="fs-120 me-2 fa-stack">
              <i class="fas fa-hexagon fa-stack-2x {{unless app.color "text-primary"}}" style={{color app.color}} ></i>
              <i class="{{app.icon}} fa-stack-1x text-white" ></i>
            </span>
            {{app.name}}
          </LinkTo>
        {{/each}}
      </div>
    </div>
    {{#if this.canEdit}}
      <div class="dropdown">
        <button class="btn rounded" type="button" id="dropdown-app" data-bs-toggle="dropdown" aria-expanded="false">
          <i class="fa fa-ellipsis" />
        </button>
        <ul class="dropdown-menu" aria-labelledby="dropdown-app">
          <li>
            <LinkTo @route="app.edit.general" @model={{this.model}} class="dropdown-item">
              <i class="fal fa-pencil fa-fw" /> {{t "edit"}}
            </LinkTo>
          </li>
        </ul>
      </div>
    {{/if}}
  </div>

  {{#if this.viewpointAppsTask.isRunning}}
    <Utility::Loader @task={{this.viewpointAppsTask}}/>
  {{else if this.userCanAccessAppInTeam}}
    <img src={{this.imageSource}} class="featured-image">

    <div class="app-header ps-4 mb-4">
      <div class="d-flex align-items-center">
        <span class="fa-stack">
          <i class="fas fa-hexagon fa-stack-2x hexagon-border"></i>
          <i class="fas fa-hexagon fa-stack-2x {{unless this.model.color "text-primary"}}" style={{color this.model.color}}></i>
          <i class="{{this.model.icon}} fa-stack-1x text-light" />
        </span>
        <div>
          <h3 class="fw-bold">{{this.model.name}}</h3>
        </div>
      </div>
      <div>
        {{this.model.shortDescription}}
      </div>
    </div>
    <div class="ps-4">
      <div class="mb-4">
        {{#if this.objectTypes.length}}
          <div class="icon-list">
            {{#each this.objectTypes as |objectType|}}
              <LinkTo @route="objects" @model={{objectType.id}}>
                <IconCard
                  @name={{objectType.name}}
                  @icon={{objectType.icon}}
                  @color={{objectType.color}}
                />
              </LinkTo>
            {{/each}}
          </div>
        {{else}}
          <div class="text-secondary m-4">{{t "no_items_to_display"}}</div>
        {{/if}}
      </div>
      {{#if this.model.longDescription}}
        <div class="fw-bold">
          {{t "app_creation.long_description"}}
        </div>
        {{htmlsafe this.model.longDescription}}
      {{/if}}
    </div>
  {{else}}
    <div class="text-center text-secondary mt-4">{{t "app.not_available"}}</div>
  {{/if}}
</div>
