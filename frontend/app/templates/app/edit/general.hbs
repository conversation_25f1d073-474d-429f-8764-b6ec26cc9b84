<div class="tab-pane active h-100 overflow-auto p-2" id="role" role="tabpanel" aria-labelledby="role-tab">
  <div class="p-3 bg-body rounded">
    <div class="form">
      <div class="row mb-3">
        <label class="col-form-label col-lg-3 text-lg-end">
          {{t "attributes.name"}}
          <TranslatableIcon
            class="p-0"
            @translatableObject={{this.model}}
            @key="name"
          />
        </label>
        <div class="col-lg-9">
          <input
            type="text"
            class="form-control"
            value={{this.model.name}}
            {{on "input" (fn this.setInputAppProperty "name")}}
          >
        </div>
      </div>
      <div class="row mb-3">
        <label class="col-form-label col-lg-3 text-lg-end">{{t "owners"}}</label>
        <div class="col-lg-9">
          <TomSelects::UserSelector
            @selected={{this.owners.selected}}
            @onChange={{this.setSelectedOwners}}
            @multiple={{true}}
          />
        </div>
      </div>
      <div class="row mb-3">
        <label class="col-form-label col-lg-3 text-lg-end">{{t "color"}}</label>
        <div class="colors col-lg-9">
          <ColorGrid @colors={{this.defaultColors}} @onClick={{this.setAppColor}} @activeColor={{this.model.color}} class="mb-3"/>
          <div class="d-flex">
            <ColorPicker @onChange={{this.setAppColor}} @value={{this.model.color}} @showClear={{true}} class="btn-sm">
              {{t "custom_color"}}
            </ColorPicker>
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <label class="col-form-label col-lg-3 text-lg-end">{{t "icon"}}</label>
        <div class="col-lg-9">
          <IconPicker @selectIcon={{fn this.setAppProperty "icon"}} @selectedIcon={{this.model.icon}} />
        </div>
      </div>
      <div class="row mb-3">
        <label class="col-form-label col-lg-3 text-lg-end">
          {{t "app.short_description"}}
          <TranslatableIcon
            @translatableObject={{this.model}}
            @key="shortDescription"
            @allowEmptyTranslations={{true}}
            class="p-0"
          />
        </label>
        <div class="col-lg-9">
          <input
            class="form-control" type="text"
            value={{this.model.shortDescription}}
            {{on "input" (fn this.setInputAppProperty "shortDescription")}}
          >
        </div>
      </div>
      <div class="row mb-3">
        <label class="col-form-label col-lg-3 text-lg-end">
          {{t "app.long_description"}}
          <TranslatableIcon
            @translatableObject={{this.model}}
            @key="longDescription"
            @isRichText={{true}}
            @allowEmptyTranslations={{true}}
            class="p-0"
          />
        </label>
        <RichTextEditor
          @value={{this.model.longDescription}}
          @onChange={{fn this.setAppProperty "longDescription"}}
          class="col-lg-9"
        />
      </div>
    </div>
    <div class="d-flex">
      <SaveButton
        class="btn-primary ms-auto"
        @callback={{this.saveApp}}
        @disabled={{this.disableSaveButton}}
      />
    </div>
  </div>
</div>
