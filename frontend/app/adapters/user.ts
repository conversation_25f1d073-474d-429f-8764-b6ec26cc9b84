import ModelRegistry from 'ember-data/types/registries/model';
import ApplicationAdapter from './application';
import Store from '@ember-data/store';

export default class UserAdapter extends ApplicationAdapter {
  urlForQuery(query: { url?: { team?: number, objectTypeId?: number, interfaceId?: number, admin?: boolean, appId?: number } }, modelName: keyof ModelRegistry) {
    if (query.url?.team) {
      const team = query.url.team;
      delete query.url;
      return `/${this.namespace}/teams/${team}/users`;
    }

    if (query.url?.objectTypeId) {
      const objectTypeId = query.url.objectTypeId;
      delete query.url;
      return `/${this.namespace}/object_types/${objectTypeId}/users`;
    } else if (query.url?.interfaceId) {
      const interfaceId = query.url.interfaceId;
      delete query.url;
      return `/${this.namespace}/interfaces/${interfaceId}/users`;
    } else if (query.url?.admin) {
      delete query.url;
      return `/${this.namespace}/admin/users`;
    } else if (query.url?.appId) {
      const appId = query.url.appId;
      delete query.url;
      return `/${this.namespace}/apps/${appId}/users`;
    }

    return super.urlForQuery(query, modelName);
  }

  async disable(store: Store, id: string) {
    const url = this.buildURL('user', id) + '/disable';
    const response = await this.ajax(url, 'PUT');
    const user = store.pushPayload(response);
    return user;
  }

  async enable(store: Store, id: string) {
    const url = this.buildURL('user', id) + '/enable';
    const response = await this.ajax(url, 'PUT');
    const user = store.pushPayload(response);
    return user;
  }
}

// DO NOT DELETE: this is how TypeScript knows how to look up your adapters.
declare module 'ember-data/types/registries/adapter' {
  export default interface AdapterRegistry {
    'user': UserAdapter;
  }
}
