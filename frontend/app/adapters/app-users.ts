import ApplicationAdapter from "./application";

export default class AppUsersAdapter extends ApplicationAdapter {
  async updateAppUser(id: string, userId: string, owner: boolean) {
    const url = this.buildURL('app', id) + `/users/${userId}`;
    return await this.ajax(url, 'PUT', { data: { attributes: { owner: owner } } });
  }
}

// DO NOT DELETE: this is how TypeScript knows how to look up your adapters.
declare module 'ember-data/types/registries/adapter' {
  export default interface AdapterRegistry {
    'app-users': AppUsersAdapter;
  }
}
