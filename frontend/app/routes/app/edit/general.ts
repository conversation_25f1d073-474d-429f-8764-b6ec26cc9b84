import Route from '@ember/routing/route';
import AppModel from 'frontend/models/app';
import Transition from "@ember/routing/transition";
import AppEditGeneralController from 'frontend/controllers/app/edit/general';
import { service } from '@ember/service';
import Store from '@ember-data/store';
import { handleError } from 'frontend/utils/handle-error';
import { Owners } from 'frontend/utils/owners';

export default class AppEditGeneralRoute extends Route {
  @service
  declare store: Store;

  async setupController(controller: AppEditGeneralController, model: AppModel, transition: Transition<unknown>) {
    super.setupController(controller, model, transition);

    controller.registerDirtyWarnings();
    try {
      const users = await this.store.query('user', { url: { appId: model.id }, filter: { owner: true } });
      controller.owners = new Owners(users.slice());
    } catch (error) {
      handleError(this, error);
    }
  }

  resetController(controller: AppEditGeneralController, isExiting: boolean, transition: Transition<unknown>) {
    super.resetController(controller, isExiting, transition);

    controller.unregisterDirtyWarnings();
  }
}
