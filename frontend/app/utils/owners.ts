import { tracked } from "@glimmer/tracking";
import CurrentUserModel from "frontend/models/current-user";
import User from "frontend/models/user";
import DialogService from "frontend/services/dialog";

export class Owners {
  @tracked
  selected: Array<User>;

  initial: Array<User>;

  constructor(owners: Array<User>) {
    this.selected = [...owners];
    this.initial = [...owners];
  }

  get removedIds() {
    return this.initial.reduce((removedOwnerIds: Array<string>, owner) => {
      if (!this.selected.includes(owner)) removedOwnerIds.push(owner.id);
      return removedOwnerIds;
    }, []);
  }

  get newIds() {
    return this.selected.reduce((newOwnerIds: Array<string>, owner) => {
      if (!this.initial.includes(owner)) newOwnerIds.push(owner.id);
      return newOwnerIds;
    }, []);
  }

  async confirmRemovingCurrentUser(user: CurrentUserModel, dialog: DialogService, text: string) {
    if (user.admin) return true;
    if (!this.removedIds.includes(user.id)) return true;

    // Show a dialog when you remove yourself
    const result = await dialog.confirm(text);

    // When cancelling removing yourself, put the current user back as a selectedOwner
    if (!result) {
      const currentUser = this.selected.find((o) => o.id == user.id);
      if (!currentUser) return false;
      this.selected.pushObject(currentUser);
    }

    return result;
  }
}
