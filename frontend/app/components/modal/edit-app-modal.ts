import { action } from '@ember/object';
import ModalComponent from 'frontend/components/modal/modal';
import EditAppModal from 'frontend/utils/modals/edit-app-modal';
import { AssistantChatSession, ENTRY_TYPE, ToolContent } from 'frontend/utils/assistant-chat-session';
import Owner from '@ember/owner';
import { service } from '@ember/service';
import { IntlService } from 'ember-intl';
import Store from '@ember-data/store';
import { ASSISTANT_ACTION_TYPES } from 'frontend/models/assistant-action';
import { handleError } from 'frontend/utils/handle-error';
import { tracked } from '@glimmer/tracking';
import JobModel from 'frontend/models/job';
import JobService from 'frontend/services/job';
import { task } from 'ember-concurrency';

interface EditAppModalSignature {
  Args: {
    modal: EditAppModal;
  };
}

const DATA_MODEL_TOOL_NAME = 'assistant_tools_suggest_data_model_tool__execute';

export default class EditAppModalComponent extends ModalComponent<EditAppModalSignature> {
  @service
  declare intl: IntlService;

  @service
  declare store: Store;

  @service('job')
  declare jobService: JobService;

  declare assistantChatSession: AssistantChatSession;

  @tracked
  declare appCreationJob?: JobModel;

  constructor(owner: Owner, args: EditAppModalSignature['Args']) {
    super(owner, args);

    this.assistantChatSession = new AssistantChatSession(this.modalContext.assistantChat);

    setInterval(() => {
      this.x = 1 - this.x;
    }, 1000);
  }

  @action
  close() {
    this.closeModal();
  }

  submit = task(async() => {
    if (!this.dataModelToolEntry?.messageId) return;

    const message = this.store.peekRecord('assistant-message', this.dataModelToolEntry.messageId);
    const action = this.store.createRecord('assistant-action', {
      actionType: ASSISTANT_ACTION_TYPES.APP_CREATION,
      message: message
    });

    try {
      await action.save();

      this.appCreationJob = action.job;
      if (this.appCreationJob) {
        this.jobService.subscribeJobActivity(this.appCreationJob, {
          completed: () => this.closeModal()
        });
      }
    } catch (e) {
      handleError(this, e);
    }
  });

  get dataModelToolEntry() {
    const chatEntries = [...this.assistantChatSession.entries];
    return chatEntries.reverse().find((entry) => {
      return entry.type == ENTRY_TYPE.TOOL &&
        (entry.content as ToolContent).tool_name == DATA_MODEL_TOOL_NAME;
    });
  }

  get isProcessingAppCreation() {
    return Boolean(this.appCreationJob?.isInProgress || this.appCreationJob?.isPending || this.submit.isRunning);
  }

  @tracked x = 1;

  get disableSubmit() {
    return this.x == 1 || this.isProcessingAppCreation || !this.dataModelToolEntry;
  }
}
