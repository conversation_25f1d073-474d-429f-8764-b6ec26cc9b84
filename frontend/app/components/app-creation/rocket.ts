import Component from '@glimmer/component';
import { tracked } from '@glimmer/tracking';
import { action } from '@ember/object';
import { service } from '@ember/service';
import { later, cancel } from '@ember/runloop';
import { IntlService } from 'ember-intl';
import Owner from '@ember/owner';

export default class RocketComponent extends Component {
  @service
  declare intl: IntlService;

  @tracked
  currentMessageIndex = 0;

  @tracked
  messageVisible = true;

  messageRotationTimer?: ReturnType<typeof later>;
  fadeTimer?: ReturnType<typeof later>;

  rotationMessages = [
    'app_creation.rotating_messages.parsing_mission_parameters',
    'app_creation.rotating_messages.calibrating_improvement_engines',
    'app_creation.rotating_messages.preparing_launchpad',
    'app_creation.rotating_messages.launching_ai_boosters',
    'app_creation.rotating_messages.houston_we_have_app'
  ];

  constructor(owner: Owner, args: object) {
    super(owner, args);

    this.startMessageRotation();
  }

  willDestroy() {
    super.willDestroy();
    this.cleanupTimers();
  }

  get currentMessage() {
    return this.intl.t(this.rotationMessages[this.currentMessageIndex]);
  }

  @action
  startMessageRotation() {
    this.currentMessageIndex = 0;
    this.messageVisible = true;
    this.scheduleNextMessage();
  }

  scheduleNextMessage() {
    this.messageRotationTimer = later(() => {
      this.fadeOutAndChangeMessage();
    }, 3000);
  }

  fadeOutAndChangeMessage() {
    // Fade out current message
    this.messageVisible = false;

    // After fade out completes, change message and fade in
    this.fadeTimer = later(() => {
      this.currentMessageIndex = this.currentMessageIndex + 1;
      this.messageVisible = true;

      // Only schedule next message change if we haven't reached the last message
      if (this.currentMessageIndex < this.rotationMessages.length - 1) {
        this.scheduleNextMessage();
      }
    }, 300); // 300ms for fade transition
  }

  cleanupTimers() {
    if (this.messageRotationTimer) {
      cancel(this.messageRotationTimer);
      this.messageRotationTimer = undefined;
    }
    if (this.fadeTimer) {
      cancel(this.fadeTimer);
      this.fadeTimer = undefined;
    }
  }
}
