---
access: Accès
active: Actif
add_calculation: Ajouter un calcul
add_custom_qr: Ajouter un code QR/barre personnalisé
add_quick_access: Ajouter un accès rapide
add_subteam: Ajouter une sous-équipe
add_to_team: Ajouter à l'équipe
add_user: Ajouter un utilisateur
admin_panel:
  invitations_page:
    delete_selected_invitations: Supprimer les invitations sélectionnées
  page_title: Administration
  users_page:
    cannot_delete_yourself: Vous ne pouvez pas vous supprimer vous-même
    cannot_disable_yourself: Vous ne pouvez pas vous désactiver
    delete_selected_users: Supprimer les utilisateurs sélectionnés
    delete_user: Supprimer l'utilisateur
    disable_selected_users: Désactiver les utilisateurs sélectionnés
administrators: Administrateurs
aggregation: Calcul
aggregations:
  average: Moyenne
  count: Nombre d'enregistrements
  maximum: Maximum
  minimum: Minimum
  pluck: Liste
  sum: Somme
all: Tout
allow_creation_new_records: Autoriser la création de nouveaux enregistrements
allow_select_existing_records: Autoriser la sélection d'enregistrements existants
and: Et
app:
  long_description: Description longue
  not_available: Cette application n'est pas disponible dans l'équipe sélectionnée.
  short_description: Description courte
app_creation:
  action: Action
  actor: Acteur
  create_app: Créer application
  description: Description
  description_placeholder: Demandez à Houston
  field_or_relationship: Champ / Relation
  generate_with_ai: Générer avec l'IA
  generating_your_app: Génération de votre application...
  key_roles: Rôles clés
  kpi_name: Nom du KPI
  kpis: Indicateurs clés de performance
  long_description: Résumé
  new_app: Nouvelle application
  relationship_to: Relation avec
  rotating_messages:
    calibrating_improvement_engines: Calibrage de vos moteurs d'amélioration...
    houston_we_have_app: Houston, nous avons une application...
    launching_ai_boosters: Lancement des propulseurs IA...
    parsing_mission_parameters: Analyse des paramètres de mission...
    preparing_launchpad: Tous les systèmes nominaux — préparation de la rampe de lancement...
  sample_prompts:
    one: "« J'ai besoin d'une application pour réaliser des audits EHS, enregistrer les observations comportementales et attribuer des mesures correctives par site et par zone. »"
    three: "« J'ai besoin d'une application pour enregistrer et résoudre les réclamations des clients liées aux livraisons, aux retards et aux marchandises endommagées dans mon entreprise de logistique. »"
    title: Quelques idées pour inspirer
    two: "« J’ai besoin d’une application pour suivre les non-conformités de qualité, enquêter sur les causes profondes et mettre en œuvre des mesures correctives. »"
  short_description: Description courte
  step: Étape
  step_by_step_description: Description étape par étape
  subtitle: Quel flux de travail souhaitez-vous améliorer ?
  title: Construisons une application !
appearance: Apparence
apply: Appliquer
assistant:
  analysis_in_progress: Analyser...
  ask_houston: Demandez à Houston
  chat_analysis_in_progress: Analyse en cours...
  chat_houston_ai: Houston IA
  chat_prompt_may_update_tabs: Votre demande peut mettre à jour d'autres onglets
  chat_prompt_placeholder: "Demandez-moi n'importe quoi : des explications supplémentaires, des modifications..."
  data_limit_exceeded: Limite de données dépassée. Pensez à utiliser moins de champs de saisie ou moins d'enregistrements.
  experimental: Expérimental
  houston: Houston
  message_processing_error: Désolé, une erreur s'est produite lors du traitement de votre message.
  question: Question
  question_placeholder: En quoi puis-je vous aider ? Je peux résumer, analyser et bien plus encore.
  toggle_input_fields: Basculer les champs de saisie
attributes:
  background: Arrière-plan
  created_at: Créé le
  id: ID global
  label: Étiquette
  last_updated_by_id: Dernière modification par
  local_id: ID dans l'objet
  name: Nom
  text: Texte
  updated_at: Dernière modification le
  user_id: Créé par
attributes_list: Champs à afficher sous forme de liste
authentication_header_explanation:
  authentication: Ce jeton d'authentification doit être fourni comme valeur d'un en-tête "Authorization"
  power_bi: <i>(En anglais)</i><ol class="text-start"><li>In Power BI Desktop, click "Get data", choose "Web" and select "Advanced"</li><li>Paste the URL above in "URL parts" </li><li>Add a header called "Authorization" in "HTTP request header parameters (optional)" and paste the Authentication token above as its value</li><li>Confirm by clicking "OK"</li></ol>
automate: Automatiser
automation:
  action: Action
  add_all: Ajouter tout
  add_attribute: Ajouter un champ
  add_input_attribute: Ajouter un champ de saisie
  add_output_attribute: Ajouter un champ de sortie
  add_recipient: Ajouter un destinataire
  advanced_settings: Réglages avancés
  automation: Automatisation
  automations: Automatisations
  create: un nouvel enregistrement est créé
  cron: à une heure prévue
  description_field: Décrire le champ à l'IA
  destroy: un enregistrement est supprimé
  email_address_from_field: Adresse e-mail saisie dans un champ
  enter_email: Entrez l'e-mail
  event: Evénement
  fill_fields_ai: Générer avec l'IA
  frontend: un bouton est cliqué
  get_static_data: Obtenir des données statiques
  input_attributes: Champs de saisie
  integration: une intégration est activée
  mentioned_users: Utilisateurs mentionnés dans l'enregistrement
  only_present_fields: Ignorer les champs vides
  only_updated_fields: Afficher uniquement les champs modifiés
  output_attributes: Champs de sortie
  recipient: Destinataire
  recipient_type: Type de destinataire
  recipients: Destinataires
  remove_all: Enlever tout
  run_integration: Exécuter l'intégration
  save_before_execute: Sauvegarder l’enregistrement avant l’exécution de l’automatisation
  schedule_options:
    after_n_minutes: Après {delay} minutes
    immediately: Immédiatement
    with_delay: Avec un délai
  scheduling: Programmation
  select_buttons: Sélectionnez les boutons
  selected_users: Utilisateurs sélectionnés
  send_based_on_field: Envoyer en fonction d'une valeur de champ
  send_email: Envoyer un e-mail
  send_immediately: Envoyer immédiatement
  send_later: Envoyer plus tard
  send_webhook: Envoyer un webhook
  show_changes: Mettre en évidence les modifications dans les valeurs des champs
  static_mail_address: Adresse e-mail
  system_message: Message système
  system_message_description: Expliquez à l'IA comment se comporter
  team_of_record: Équipe de l'enregistrement
  type_of_action: Type d'action
  type_of_event: Type d'événement
  update: un enregistrement est mis à jour
  update_attributes: Champs à mettre à jour
  update_record: Mettre à jour l'enregistrement
  user_selected_in_a_field: Utilisateur sélectionné dans un champ
  webhook_payload_fields: Champs à inclure dans la charge utile de la requête
background: Arrière-plan
bars: Barres
body: Corps
calculation:
  calculated: Calculé
  create_calculation: Créer un calcul
  edit_calculation: Modifier le calcul
  enter_formula: Saisir une formule
  formula: Formule
  formula_explanation: |-
    <p>Les formules sont des outils puissants pour effectuer des calculs et analyser des données dans Houston.</p><p>Les formules suivent une syntaxe de type Excel :</p> <ul> <li>Une formule est une expression (mathématique) qui se compose de nombres/texte, d'opérateurs mathématiques, de <strong>variables</strong> et de <strong>fonctions</strong> qui peuvent être imbriquées. Par exemple :</li> <ul> <li>A + 10</li> <li>A / B * 2</li> <li>IF(A > B, TRUE, FALSE)</li> < li>CONCAT(A + B, 'kg au total')</li></ul>
    <li>Une <strong>variable</strong> est une référence à un champ dans un objet. La variable est un caractère alphabétique (A, B, C, etc.) pouvant être utilisé dans la formule. Lorsque la formule est évaluée, la variable est remplacée par la valeur du champ dans l'enregistrement.</li>
    <li>Une <strong>fonction</strong> est un raccourci intégré permettant d'effectuer un calcul ou une autre opération sur les données. De nombreuses fonctions sont disponibles et la liste ne cesse de s'allonger. Cet assistant vise à vous aider à trouver exactement la fonction Houston qui correspond à vos besoins. <NAME_EMAIL> si vous constatez le besoin d'une fonction supplémentaire.</li></ul>
  how_to_write_formula: Comment écrire une formule dans Houston ? »
  selectable_values_number_map: Valeur pour les calculs
  variable: Variable
cancel: Annuler
categories: Catégories
charts:
  area: Zone
  axis: Axe
  bar: Barre
  bar_stack_id: ID du groupe de barres
  category: Catégorie
  data_serie_options: Série de données
  date_field: Champ de date
  display_value: Afficher valeur
  legend: Légende
  line: Ligne
  opening_angle: Angle d'ouverture
  rotation: Rotation
  segment: Segment
  show_as_donut: Afficher sous forme de donut ?
  target: Cible
  targets:
    above: Au-dessus de
    below: Au-dessous de
    between: Entre
    lower: Minimum
    outside: Dehors
    range: Intervalle
    single_value: Valeur
    upper: Maximum
  time_frame: Période de temps
  time_unit: Unité de temps
  x_axis: Axe X
  y_axis: Axe Y
choose_or_drop_files: Choisissez les fichiers ou déposez les fichiers ici
clear: Effacer
clone: Cloner
close: Fermer
color: Couleur
columns: Colonnes
comment:
  comment: Commentaire
  deleted_user: "<b>Utilisateur supprimé</b> a commenté"
  deleted_user_name: "Utilisateur Supprimé"
  edited: modifié
  loading: Chargement des commentaires ...
  placeholder: Laissez un commentaire. Utilisez @ pour mentionner un autre utilisateur.
  user: "<b>{user}</b> a commenté"
  you: "<b>Vous</b> avez commenté"
condition:
  always: Toujours
  and_join: " et "
  blank_value: "(vide)"
  condition: Condition
  conditions: Conditions
  display_types:
    disable: désactive
    filter_selectable: filtrer les options
    hide: cache
    show: affiche
  field_with_name: champ "{name}"
  if: Si
  operators:
    blank: est vide
    changes: Change
    changes_into: Change en
    contains: contient
    equals: est égal à
    gt: est superieur à
    in: dans
    lt: est inférieur à
    not_equals: pas égal à
    presence: est rempli
  or_join: " ou "
  result_selectable_explanation: affiche {values} dans {subject}
  result_visibility_explanation: "{action} {subject}"
  rule: Critère
  rule_clause_explanation: '"{field}" {operator}'
  rule_clause_explanation_with_value: '"{field}" {operator} "{value}"'
  section_with_name: section "{name}"
  tab_with_name: Onglet "{name}"
  then: alors
  when: Si
conditional_colors: Formatage conditionnel
conditional_displays: Affichages conditionnels
confirm: Confirmer
connected_elements: Éléments connectés
contact_support: Contacter support
copied: Copié !
copy: Copier
create: Créer
create_data_source: Créer une source de données
create_interface: Créer une interface
create_lookup_field: Créer un champ de recherche à partir d'un objet lié
create_new_interface: Créer une nouvelle interface
create_new_object_type: Créer un nouvel objet
create_object_type: Créer un objet
create_subteam: Créer une sous-équipe
create_view: Créer une vue
cron_settings:
  applied_objects: Pour les enregistrements sélectionnés
  every_day_at: Tous les jours à
  every_week_at: Chaque semaine à
  interval_type: Type d'intervalle
  timing: Timing
custom: Personnalisé
custom_color: Couleur personnalisée
data: Données
data_source:
  auth_token: Jeton d'authentification
datafeed_field_identifier: ID pour l'exportation de données
date_period: Date période
deactivated: Désactivé
default: Défaut
default_value: Valeur par défaut
delete: Supprimer
delete_interface: Supprimer l'interface
delete_invitations_from_team: Retirer les invitations sélectionnées de cette équipe
delete_object: Supprimer l'objet
delete_record: Supprimer l'enregistrement
delete_selected: Supprimer la sélection
delete_selected_users_from_team: Retirer les utilisateurs sélectionnés de cette équipe
delete_user_from_team: Retirer l'utilisateur de cette équipe
delivery: Livraison
description: Description
dialog:
  are_you_sure: Êtes-vous sûr ?
  confirm_accept_requests: "{itemCount, plural, one {Cette demande d'adhésion sera acceptée} other {Les demandes d'adhésion sélectionnées ({itemCount}) seront acceptées} }. Êtes-vous sûr ?"
  confirm_delete_attachment: Cette pièce jointe ({attachmentName}) sera définitivement supprimée. Êtes-vous sûr ?
  confirm_delete_automation: Cette automatisation ({automationName}) sera définitivement supprimée. Êtes-vous sûr ?
  confirm_delete_comment: Ce commentaire sera définitivement supprimé. Êtes-vous sûr ?
  confirm_delete_condition: Cette condition sera définitivement supprimée. Êtes-vous sûr ?
  confirm_delete_empty_form_tab: Cet onglet ({name}) sera définitivement supprimé. Êtes-vous sûr ?
  confirm_delete_empty_section: Cette section ({name}) sera définitivement supprimée. Êtes-vous sûr ?
  confirm_delete_form_element: Cet élément de formulaire ({name}) sera définitivement supprimé. Êtes-vous sûr ?
  confirm_delete_form_tab: Cet onglet ({name}) sera définitivement supprimé. Êtes-vous sûr ? Veuillez saisir <b>{confirmText}</b> pour supprimer définitivement cet onglet.
  confirm_delete_interface: Cette interface ({name}) sera définitivement supprimée et ne pourra pas être récupérée. Êtes-vous sûr ? Ceci entraînera une PERTE DE DONNÉES irréparable. Veuillez saisir <b>{confirmText}</b> pour supprimer définitivement cette interface.
  confirm_delete_interface_element: Cet élément sera définitivement supprimé. Êtes-vous sûr ?
  confirm_delete_invites: "{itemCount, plural, one {Cette invitation sera supprimée} other {Les invitations sélectionnées ({itemCount}) seront supprimées} }. Êtes-vous sûr ?"
  confirm_delete_link_tab: Cet onglet et son contenu seront définitivement supprimés. Êtes-vous sûr ?
  confirm_delete_nested_grid: Ce groupe d'éléments et son contenu seront définitivement supprimés. Êtes-vous sûr ?
  confirm_delete_object_type: Cet objet ({name}) et tous ses enregistrements qu'il contient seront définitivement supprimés et ne pourront pas être récupérés. Êtes-vous sûr ? Ceci entraînera une PERTE DE DONNÉES irréparable. Veuillez saisir <b>{confirmText}</b> pour supprimer cet objet définitivement.
  confirm_delete_object_type_role: Ce rôle ({objectTypeRoleName}) sera définitivement supprimé. Êtes-vous sûr ?
  confirm_delete_objects: "{itemCount, plural, one {Cet enregistrement sera définitivement supprimé et ne pourra pas être récupéré} other {Les enregistrements sélectionnés ({itemCount}) seront définitivement supprimés et ne pourront pas être récupérés} }. Êtes-vous sûr ?"
  confirm_delete_option: Cette option sera définitivement supprimée. Êtes-vous sûr ?
  confirm_delete_quick_access: Cet accès rapide ({quickAccessName}) sera définitivement supprimé. Êtes-vous sûr ?
  confirm_delete_section: Cette section ({name}) sera définitivement supprimée. Êtes-vous sûr ? Veuillez saisir <b>{confirmText}</b> pour supprimer définitivement cette section.
  confirm_delete_signature: Cette signature sera définitivement supprimée. Êtes-vous sûr ?
  confirm_delete_users: "{itemCount, plural, one {Cet utilisateur sera définitivement supprimé} other {Les utilisateurs sélectionnés ({itemCount}) seront définitivement supprimés} }. Êtes-vous sûr ?"
  confirm_delete_users_from_team: "{itemCount, plural, one {Cet utilisateur sera définitivement retiré} other {Les utilisateurs sélectionnés ({itemCount}) seront définitivement retirés} } de cette équipe. Êtes-vous sûr ?"
  confirm_delete_validation: Cette validation sera définitivement supprimée. Êtes-vous sûr ?
  confirm_delete_view: Cette vue ({viewName}) sera définitivement supprimée. Êtes-vous sûr ?
  confirm_discard_custom_thumbnail: Cela supprimera la miniature personnalisée actuelle. Êtes-vous sûr ?
  confirm_lose_rights: Les modifications que vous avez apportées peuvent affecter les utilisateurs affectés à ce rôle. Êtes-vous sûr ?
  confirm_overwrite_custom_qr: Un code QR/barre personnalisé a déjà été associé à cet enregistrement. Voulez-vous l'écraser ?
  confirm_publish_object_type: Cet objet contient encore des informations manquantes. Cela peut avoir un impact sur son comportement et/ou sa convivialité. Êtes-vous sûr ?
  confirm_recalculation:
    message: La formule associée à ce champ a été modifiée. Souhaitez-vous recalculer les valeurs de ce champ pour tous les enregistrements existants ?<br>Ce recalcul ne déclenchera aucune automatisation.
    title: Recalculer ?
  confirm_reject_requests: "{itemCount, plural, one {Cette demande d'adhésion sera rejetée } other {Les demandes d'adhésion sélectionnées ({itemCount}) seront rejetées} }. Êtes-vous sûr ?"
  confirm_removal_current_user_as_interface_owner: Vous êtes retirés de la liste des propriétaires et vous ne pourrez plus modifier cette interface. Êtes-vous sûr ?
  confirm_removal_current_user_as_object_type_owner: Vous êtes retirés de la liste des propriétaires et vous ne pourrez plus modifier cet objet. Êtes-vous sûr ?
  confirm_remove_interface: Êtes-vous sûr de vouloir retirer <b>{name}</b> de <b>{teamName}</b> ?
  confirm_remove_object_type: Êtes-vous sûr de vouloir retirer <b>{name}</b> de <b>{teamName}</b> ?
  confirm_trigger_event: Cet enregistrement contient des modifications non sauvegardées. Souhaitez-vous sauvegarder l'enregistrement et exécuter l'action ?
  confirm_unpublish_interface: Êtes-vous sûr de vouloir annuler la publication de <b>{name}</b> dans cette équipe ? Les membres ne pourront y accéder ni à ses données. Êtes-vous sûr ?
  confirm_unpublish_object_type: Êtes-vous sûr de vouloir annuler la publication de <b>{name}</b> dans cette équipe ? Les membres ne pourront y accéder ni à ses données. Êtes-vous sûr ?
  confirm_unsaved_changes_app: Cette application comporte des modifications non sauvegardées. Êtes-vous sûr de vouloir fermer sans sauvegarder ?
  confirm_unsaved_changes_automation:
    text_close: Cette automatisation comporte des modifications non sauvegardées. Êtes-vous sûr de vouloir fermer sans sauvegarder ?
    text_save: Cette automatisation comporte des modifications non sauvegardées. Êtes-vous sûr de sauvegarder ?
  confirm_unsaved_changes_automation_action:
    text_close: Cette action comporte des modifications non sauvegardées. Voulez-vous vraiment fermer sans sauvegarder ?
    text_save: Cette action comporte des modifications non sauvegardées. Êtes-vous sûr de sauvegarder ?
  confirm_unsaved_changes_conditional_display: Cette condition comporte des modifications non sauvegardées. Êtes-vous sûr de vouloir fermer sans sauvegarder ?
  confirm_unsaved_changes_interface: Cette interface comporte des modifications non sauvegardées. Êtes-vous sûr de vouloir fermer sans sauvegarder?
  confirm_unsaved_changes_object: Cet enregistrement comporte des modifications non sauvegardées. Êtes-vous sûr de vouloir fermer sans sauvegarder ?
  confirm_unsaved_changes_record_picker: Cet enregistrement comporte des modifications non enregistrées. Êtes-vous sûr de vouloir sélectionner un autre enregistrement et ignorer les modifications non enregistrées ?
  confirm_unsaved_changes_role: Ce rôle comporte des modifications non sauvegardées. Êtes-vous sûr de vouloir fermer sans sauvegarder ?
  confirm_unsaved_changes_show_route_record_picker: Des modifications non enregistrées existent pour {records}. Êtes-vous sûr de vouloir les supprimer ?
  confirm_unsaved_changes_team: Cette équipe comporte des modifications non sauvegardées. Êtes-vous sûr de vouloir fermer sans sauvegarder ?
  confirm_unsaved_changes_thumbnail: Cette miniature comporte des modifications non sauvegardées. Êtes-vous sûr de vouloir fermer sans sauvegarder ?
  confirm_unsaved_form: Ce formulaire comporte des modifications non sauvegardées. Êtes-vous sûr de vouloir fermer sans sauvegarder ?
  confirm_unsaved_interface: Cette interface comporte des modifications non sauvegardées. Êtes-vous sûr de vouloir fermer sans sauvegarder ?
  confirm_unsaved_translations:
    target_text: La traduction cible n'est pas sauvegardée. Êtes-vous sûr de vouloir modifier les paramètres régionaux cibles sans sauvegarder ?
    text: Ces traductions ne sont pas sauvegardées. Êtes-vous sûr de vouloir fermer sans sauvegarder ?
  confirm_unshare_object_type: Êtes-vous sûr de vouloir annuler la diffusion de <b>{name}</b> dans <b>{teamName}</b> ?
  confirmation_required: Confirmation requise
display: Affichage
display_as: Afficher comme
do: Exécute
done: Terminé
download: Télécharger
dynamic: Dynamique
dynamic_options:
  current_user: Utilisateur connecté
  now: Date et heure actuelles
  today: Date actuelle
edit: Modifier
edit_filter_rules: Modifier les filtres
edit_object: Modifier l'objet
edit_shortcut: Modifier le lien
edit_sort_rules: Modifier les règles de tri
edit_tab: Modifier l'onglet
edit_view: Modifier la vue
embed: Intégrer
embed_modal:
  show_tabs: Afficher le sélecteur d'onglets
  show_toolbar: Afficher la barre d’outils
enable_scanning: Activer la numérisation
enter_number: Saisir un nombre
enter_something: Saisir quelque chose
enter_text: Saisir du texte
enter_url: Saisir un lien hypertexte
error:
  empty_interface_owners_list: L'interface doit avoir au moins un propriétaire. Veuillez ajouter un nouveau propriétaire.
  empty_object_type_owners_list: L’objet doit avoir au moins un propriétaire. Veuillez ajouter un nouveau propriétaire.
  generic_server_message: Une erreur s’est produite lors du traitement de la requête.
  object_type_attributes_limit: Vous avez atteint le maximum de {object_type_attributes_limit} champs par objet. Veuillez envisager de créer des objets séparés avec moins de champs chacun et de lier ces objets.
  title: Erreur
export:
  cancelled: Exportation annulée
  completed: Exportation terminée
  export: Exporter
  failed: Échec de l'exportation
  in_progress: Exportation en cours
'false': Non
field: Champ
field_calculation: Calcul de champ
field_name: Nom du champ
field_name_placeholder: Saisir le nom du champ
field_to_display: Valeur de champ à afficher
field_to_search_by: Champ de recherche
filters_applied: "{amount, plural, =0 {Aucun filtre appliqué} =1 {# filtre appliqué} other {# filtres appliqués}}"
fit_element: Ajuster à l'élément (Seul le premier aperçu sera affiché)
form: Formulaire
form_builder:
  label_for_hidden: Cacher le champ dans cette variante
  label_for_locked: Affichage forcé dans les variantes
  object_type_relationship_options:
    cardinalities:
      multiple: Un <baseObjectType> peut être lié à plusieurs <targetObjectType>
      single: Un <baseObjectType> peut être lié à au plus un <targetObjectType>
    forward: Relation directe
    from: Depuis
    inverse: Relation inverse
    object_already_linked: Objet déjà lié
    object_not_yet_linked: Objet pas encore lié
    to: Vers
  selectable_value_options:
    allow_variant_options: Possibilité d'ajouter des options dans les variantes
formula:
  ABS:
    description: Renvoie la valeur absolue d'un nombre, un nombre sans son signe.
    syntax_description: "<ul><li>Number : est le nombre réel pour lequel trouver la valeur absolue.</li></ul>"
  ALL:
    description: Vérifie si tous les arguments sont TRUE et renvoie TRUE si tous les arguments sont TRUE.
    syntax_description: "<ul><li>List : liste de valeurs.</li> <li>Parameter : nom du paramètre utilisé dans la fonction.</li> <li>Function : appliquée à chaque valeur (paramètre) et vérifie si toutes les valeurs répondent à une certaine condition.</li></ul>"
  AND:
    description: Vérifie si tous les arguments sont TRUE et renvoie TRUE si tous les arguments sont TRUE.
    syntax_description: "<ul><li>Logical1, Logical2,... : toutes valeurs ou expressions pouvant être évaluées comme TRUE ou FALSE.</li></ul>"
  ANY:
    description: Vérifie si tous les arguments sont TRUE et renvoie TRUE si l'un des arguments est TRUE.
    syntax_description: "<ul><li>List : liste de valeurs.</li> <li>Parameter : nom du paramètre utilisé dans la fonction.</li> <li>Function : appliquée à chaque valeur (paramètre) et vérifie si la valeur répond à une certaine condition.</li></ul>"
  AVG:
    description: Renvoie la moyenne (moyenne arithmétique) de ses arguments.
    syntax_description: "<ul><li>Number1, Number2, ... : arguments numériques pour lesquels calculer la moyenne.</li></ul>"
  CONCAT:
    description: Concatène une liste de chaînes de texte.
    syntax_description: "<ul><li>Text1, Text2, ... : chaînes à joindre à une seule chaîne de texte.</li></ul>"
  CONTAINS:
    description: Vérifie si un sous-texte se trouve dans un texte et renvoie TRUE ou FALSE.
    syntax_description: "<ul><li>Find_text : est le texte à rechercher.</li> <li>Within_text : est le texte contenant le texte à rechercher.</li></ul>"
  COS:
    description: Renvoie le cosinus d'un angle.
    syntax_description: "<ul><li>Number : est l'angle en radians pour lequel calculer le cosinus.</li></ul>"
  COUNT:
    description: Compte le nombre de valeurs ou la longueur d'une chaîne.
    syntax_description: "<ul><li>Value1, Value2, ... : valeurs qui peuvent être de différents types de données.</li></ul>"
  COUNTIF:
    description: Compte toutes les valeurs d'une liste qui remplissent une certaine condition.
    syntax_description: '<ul><li>List : liste de valeurs.</li> <li>Function : appliquée à chaque valeur (paramètre) et compte chaque valeur qui répond à une certaine condition. Le nom du paramètre qui doit être utilisé dans la fonction est : "value".</li></ul>'
  DURATION:
    description: Renvoie une valeur à ajouter ou à soustraire d'une date.
    syntax_description: "<ul><li>Number: est le nombre le nombre d'unités.</li><li>Unit: 'years', 'months' et 'days' sont les unités disponibles.</li></ul>"
  EXP:
    description: Renvoie e élevé à la puissance d’un nombre donné.
    syntax_description: "<ul><li>Number : est l'exposant appliqué à la base e. La constante e est égale à 2,71828182845904, la base du logarithme népérien.</li></ul>"
  FILTER:
    description: Filtre une liste de valeurs.
    syntax_description: "<ul><li>List : liste de valeurs.</li> <li>Parameter : nom du paramètre utilisé dans la fonction.</li> <li>Function : appliquée à chaque valeur (paramètre) et filtre les valeurs qui ne remplissent pas une certaine condition.</li></ul>"
  FIND:
    description: Renvoie la position d'un sous-texte dans un texte.
    syntax_description: "<ul><li>Find_text : est le texte à rechercher.</li> <li>Within_text : est le texte contenant le texte à rechercher.</li></ul>"
  IF:
    description: Vérifie si une condition a été remplie et renvoie une valeur si TRUE et une autre valeur si FALSE.
    syntax_description: "<ul><li>Logical_test : est toute valeur ou expression qui peut être évaluée comme TRUE ou FALSE.</li> <li>Value_if_true : est la valeur qui sera renvoyée si Logical_test est TRUE. En cas d'omission, TRUE sera renvoyé.</li> <li>Value_if_false : est la valeur qui sera renvoyée si Logical_test est FALSE. En cas d'omission, FALSE sera renvoyé.</li></ul>"
  LEFT:
    description: Renvoie le nombre spécifié de caractères à partir du début d'une chaîne de texte.
    syntax_description: "<ul><li>Text : est la chaîne de texte contenant les caractères à extraire.</li> <li>Num_chars : spécifie le nombre de caractères à extraire.</li></ul>"
  LEN:
    description: Renvoie le nombre de caractères dans une chaîne de texte.
    syntax_description: "<ul><li>Text : est le texte dont il faut calculer la longueur. Les espaces comptent comme des caractères.</li></ul>"
  LOG:
    description: Renvoie le logarithme d'un nombre dans la base que vous spécifiez.
    syntax_description: "<ul><li>Number : est le nombre réel positif pour lequel vous voulez le logarithme.</li><li>Base : est la base du logarithme ; e si omis.</li></ul>"
  LOG10:
    description: Renvoie le logarithme base 10 d'un nombre.
    syntax_description: "<ul><li>Number : est le nombre réel positif pour lequel vous souhaitez le logarithme en base 10.</li></ul>"
  MAP:
    description: Renvoie une liste de valeurs formée en « mappant » chaque valeur de la liste à une nouvelle valeur en appliquant une fonction.
    syntax_description: "<ul><li>List : liste de valeurs.</li> <li>Parameter : nom du paramètre utilisé dans la fonction.</li> <li>Function : appliquée à chaque valeur (paramètre) et renverra le nouvelle valeur calculée.</li></ul>"
  MAX:
    description: Renvoie la plus grande valeur dans un ensemble de valeurs.
    syntax_description: "<ul><li>Number1, Number2, ... : nombres pour lesquels trouver le maximum.</li></ul>"
  MAXOCCURRENCE:
    description: Renvoie la n-ième valeur la plus courante dans une liste.
    syntax_description: "<ul><li>Liste : liste de valeurs.</li> <li>n : index de la valeur la plus courante à trouver.</li></ul>"
  MID:
    description: Renvoie les caractères du milieu d'une chaîne de texte, en fonction d'une position de départ et d'une longueur.
    syntax_description: "<ul><li>Text : est la chaîne de texte à partir de laquelle extraire les caractères.</li> <li>Start_num : est la position du premier caractère à extraire. Le premier caractère de Text est 1.</li> <li>Num_chars : spécifie le nombre de caractères à renvoyer depuis Text.</li></ul>"
  MIN:
    description: Renvoie le plus petit nombre dans un ensemble de valeurs.
    syntax_description: "<ul><li>Number1, Number2, ... : nombres pour lesquels trouver le minimum.</li></ul>"
  NOT:
    description: Change FALSE en TRUE ou TRUE en FALSE.
    syntax_description: "<ul><li>Logical : est une valeur ou une expression qui peut être évaluée comme TRUE ou FALSE.</li></ul>"
  OR:
    description: Vérifie si l'un des arguments est TRUE et renvoie TRUE ou FALSE. Renvoie FALSE uniquement si tous les arguments sont FALSE.
    syntax_description: "<ul><li>Logical1, Logical2, ... : conditions à tester qui peuvent être TRUE ou FALSE.</li></ul>"
  RIGHT:
    description: Renvoie le nombre spécifié de caractères à partir de la fin d'une chaîne de texte.
    syntax_description: "<ul><li>Text : est la chaîne de texte contenant les caractères à extraire.</li> <li>Num_chars : spécifie le nombre de caractères à extraire.</li></ul>"
  ROUND:
    description: Arrondit un nombre à un nombre spécifié de chiffres.
    syntax_description: "<ul><li>Number : est le nombre à arrondir.</li> <li>Num_digits : est le nombre de chiffres auquel arrondir.</li></ul>"
  ROUNDDOWN:
    description: Arrondit un nombre vers le bas, vers zéro.
    syntax_description: "<ul><li>Number : est un nombre réel à arrondir.</li> <li>Num_digits : est le nombre de chiffres auquel arrondir.</li></ul>"
  ROUNDUP:
    description: Arrondit un nombre, loin de zéro.
    syntax_description: "<ul><li>Number : est un nombre réel à arrondir.</li> <li>Num_digits : est le nombre de chiffres auquel arrondir.</li></ul>"
  SIN:
    description: Renvoie le sinus d'un angle.
    syntax_description: "<ul><li>Number : est l'angle en radians pour lequel calculer le sinus.</li></ul>"
  SQRT:
    description: Renvoie la racine carrée d'un nombre.
    syntax_description: "<ul><li>Number : est le nombre dont vous voulez la racine carrée.</ul></li>"
  STDEV:
    description: Renvoie l’écart type basé sur un échantillon.
    syntax_description: "<ul><li>Number1, Number2, ... : nombres pour lesquels trouver l'écart type de l'échantillon.</li></ul>"
  SUBSTITUTE:
    description: Remplace le texte existant par un nouveau texte dans une chaîne de texte.
    syntax_description: "<ul><li>Text : est le texte dans lequel remplacer les caractères.</li> <li>Old_text : est le texte existant à remplacer.</li> <li>New_text : est le texte par lequel remplacer Old_text. </li></ul>"
  SUM:
    description: Additionne tous les nombres.
    syntax_description: "<ul><li>Number1, Number2, ... : les nombres à ajouter.</li></ul>"
  SWITCH:
    description: Évalue une expression par rapport à une liste de valeurs et renvoie le résultat correspondant à la première valeur correspondante. S'il n'y a aucune correspondance, une valeur par défaut facultative est renvoyée.
    syntax_description: "<ul><li>Expression : est une expression à évaluer.</li><li>Value1, Value2, ... : valeur à correspondre à l'expression.</li><li>Result1, Result2, .. . : valeur à renvoyer s'il y a une correspondance.</li><li>Default_value: valeur à renvoyer s'il n'y a pas de correspondance.</li></ul>"
  TAN:
    description: Renvoie la tangente d'un angle.
    syntax_description: "<ul><li>Number : est l'angle en radians pour lequel calculer la tangente.</li></ul>"
  VAR:
    description: Renvoie la variance basée sur un échantillon.
    syntax_description: "<ul><li>Number1, Number2, ... : nombres pour lesquels trouver la variance de l'échantillon.</li></ul>"
  XOR:
    description: Renvoie un « OU exclusif » logique de tous les arguments.
    syntax_description: "<ul><li>Logical1, Logical2, ... : conditions à tester qui peuvent être TRUE ou FALSE.</li> </ul>"
  syntax: Syntaxe
function_assistant: Assistant de fonction
general: Général
got_it: J'ai compris
grid: Grille
grouped_by: Groupé par
help: Aide
hide_all: Tout cacher
how_to_use_in: Comment utiliser avec
icon: Icône
include: Inclure
include_descendants: Inclure les descendants
incorrect_qr_format: Format QR incorrect
input: Champs
input_type:
  attachment: Pièce jointe
  button: Bouton
  checkbox: Case à cocher
  checkmark: Coche
  date: Date
  datetime: Date et heure
  description: Texte fixe
  descriptive_attachment: Images / medias fixes
  dropdown_list: Liste déroulante
  linked_object: Objet Houston lié
  lookup: Champ d'un objet Houston lié
  number: Nombre
  radio: Bouton radio
  range_select: Intervalle
  rich_text: Texte mis en forme
  short_text: Texte court
  signature: Signature
  switch: Interrupteur
  textarea: Zone de texte
  type: Type de champ
  url: Lien hypertexte
  user: Utilisateur
insert_function: Insérer la fonction
interface:
  attachment: Images / medias fixes
  button: Bouton
  calendar_chart: Graphique calendrier
  contact_text: Cet élément n'est pas encore configurable dans l'interface utilisateur. Besoin d'aide ? Contactez-nous à <a href="mailto:<EMAIL>"><EMAIL></a>
  filter: Filtre
  filter_through_element: ET filtrer à travers
  gage_chart: Graphique jauge
  html: HTML
  html_placeholder: Entrez du code HTML
  interface: Interface
  interface_not_available: Cette interface n'est pas disponible dans l'équipe sélectionnée.
  linebar_chart: Graphique ligne/barre/zone
  links: Liens
  nested_grid: Groupe d'éléments
  number: Nombre
  pie_chart: Graphique en secteurs/anneau
  pyramid_chart: Graphique pyramidal
  radar_chart: Graphique radar
  read_only: Lecture seule
  record_in_record_picker: Enregistrer « {record} » dans le sélecteur d'enregistrements « {record_picker} »
  record_picker: Sélecteur d'enregistrements
  show_label: Afficher l'étiquette
  source: Source
  text: Texte
  unit: Unité
interface_editor:
  action_type_selector:
    data_change: Modifier des données
    delete_record_action: Supprimer un enregistrement
    external_url_navigation: Ouvrir URL externe
    interface_section_navigation: Ouvrir onglet de l'interface
    navigation: Naviguer
    place_holder: Sélectionnez un type d'action
    save_record_action: Sauvegarder un enregistrement
    trigger_automation_action: Déclencher une automatisation
  actions:
    navigation_action:
      open_in_new_tab: Ouvrir dans un nouvel onglet
interfaces_managed_by_me: Interfaces sous ma gestion
invitation:
  add_message: Ajouter un message... (recommandé)
  date_and_time: Date et heure de l'invitation
  delete_invite: Supprimer l'invitation
  email: E-mail
  invite_user_by_email: Inviter un utilisateur par email
  invites: Invitations
  message: Message
  send_invite: Envoyer une invitation
  toast:
    invitation_sent: Invitation envoyée
    title: Invitation d'un utilisateur
    user_already_in_team: Cet utilisateur est déjà membre de l'équipe
    user_found: Cette adresse e-mail correspond à l'utilisateur {name} qui a été ajouté à l'équipe
label_display: Affichage des étiquettes
language: Langue
languages:
  en: Anglais
  es: Espagnol
  fr: Français
  nl: Néerlandais
last_activity: Dernière Activité
layout: Mise en page
less: moins
linked_fields: Champs liés
linked_object: Objet lié
linked_object_field: Champ d'objet lié
linked_objects: Objets liés
loading: Chargement...
lookup_from: Recherche à partir de
lookup_through_field: via le champ "{field}"
max: Max
members: Membres
memberships_awaiting_approval: Adhésions en attente d’approbation
message: Message
min: Min
minutes: Minutes
missing_video_permission: Aucune caméra n'est présente ou l'autorisation n'a pas été donnée.
more: plus
more_info: Plus d'infos
move: Déplacer
move_form_element_modal_title: Déplacer le champ vers un autre onglet
move_interface_modal_title: Déplacer l'élément vers ...
multiple: Multiple
my_teams: Mes équipes
navigation:
  apps: Applications
  home: Accueil
  interfaces: Interfaces
  not_found: Non trouvé
  objects: Objets
new: Nouveau
new_interface: Nouvelle interface
new_object_type: Nouvel objet
new_section: Nouvelle section
new_shortcut: Nouveau lien
new_tab: Nouvel onglet
new_version_info: Une nouvelle version est disponible.
new_version_refresh: Actualiser la page
no_access: Pas d'accès
no_access_tooltip: Vous n'êtes pas membre de cette équipe
no_filters: Aucun filtre
no_grouped_attribute_warning: Aucun champ sélectionné pour le regroupement des enregistrements.
no_home_page_description: Aucune page d'accueil définie pour cette équipe.
no_items_to_display: Aucun élément à afficher.
no_results_found: Aucun résultat trouvé
no_team_filters: Aucun filtre d'équipe
non_editable_view: Cette vue n'est pas modifiable
none: Aucun
not_active: Inactif
not_found_description: La page que vous recherchez est introuvable.
nothing_to_display: Rien à afficher.
notify_team_admins: Informer les administrateurs de l'équipe
object_form_link: Lien vers le formulaire
object_hyperlink: Lien vers l'enregistrement
object_not_available: Cet objet n'est pas disponible dans l'équipe sélectionnée.
object_print_modal:
  all_tabs: Tous les onglets
  current_tab: Onglet actuel
object_type: Objet
object_type_description:
  editor: Informations pour les éditeurs d'objets
  empty: "(Pas encore de description)"
  user: Informations pour tous les utilisateurs
object_type_share_violation: L'objet a déjà été partagé avec l'équipe.
objects_managed_by_me: Objets sous ma gestion
ok: Ok
'on': À
operations:
  after: Après
  before: Avant
  blank: Est vide
  contains: Contient
  equals: Egal à
  greater_than: Supérieur à
  in: Dans
  lower_than: Inférieur à
  not_contains: Ne contient pas
  not_equals: Pas égal à
  presence: Est rempli
  within: Dans
operator: Opérateur
option: Option
options: Options
or: Ou
other: Autre
owned: Sous ma gestion
owners: Propriétaires
personal: Personnel
pin_tab: Épingler un onglet
placeholder: Texte de substitution
position:
  inside: À l'intérieur
  top: En haut
power_bi: Power BI
print: Imprimer
properties: Propriétés
publication_settings: Paramètres de publication
publish: Publier
publish_dialog:
  default_assignment: Attribuez automatiquement les rôles de Créateur et d'Editeur à cette équipe
  descendants: Cette équipe et tous ses descendants
  interface: Interface de publication
  no_default_assignment: Laissez-moi choisir les attributions de rôles
  object: Publier un objet
  redirect: Amenez-moi à la page Rôles après la publication
  role_info: Afin de créer, mettre à jour et supprimer des enregistrements pour un objet, les utilisateurs doivent disposer d'un rôle ayant ce droit
  team: Cette équipe seulement
publish_in: Publier dans
publish_warnings:
  automation: Cette automatisation comporte des informations manquantes. Cela peut avoir un impact sur son comportement et/ou sa convivialité.
  automations: Il existe un ou plusieurs automatisations pour lesquels des informations manquent. Cela peut avoir un impact sur leur comportement et/ou leur convivialité.
  form_element: Ce champ contient des informations manquantes. Cela peut avoir un impact sur son comportement et/ou sa convivialité.
  form_elements: Il y a un ou plusieurs champs qui contiennent des informations manquantes. Cela peut avoir un impact sur leur comportement et/ou leur convivialité.
  role: Ce rôle comporte des informations manquantes. Cela peut avoir un impact sur son comportement et/ou sa convivialité.
  roles: Il existe un ou plusieurs rôles pour lesquels des informations manquent. Cela peut avoir un impact sur leur comportement et/ou leur convivialité.
published: Publié
qr_bar_code: Code QR/barre
qr_download: Télécharger le code QR de Houston
qr_modal:
  placeholder: Saisir le code
  title: Saisir le code QR/barre pour l'enregistrement
query:
  current: En cours
  current_month: Mois en cours
  current_week: Semaine en cours
  current_year: Année en cours
  day: Jour
  exact_date: Date exacte
  filter: Filtrer
  filter_by_this_field: Filtrer selon ce champ
  from: Depuis
  from_team: De l'équipe
  last_month: Mois dernier
  last_week: Semaine dernière
  last_year: Année dernière
  linked_with: Lié à
  month: Mois
  new_filter_condition: Nouveau filtre
  new_sort_option: Nouvelle règle de tri
  new_team_filter_condition: Nouveau filtre d'équipe
  new_view: Nouvelle vue
  next: Prochain
  no_filters_applied: Aucun filtre n'est actif.
  no_sorts_applied: Aucune règle de tri n'est active.
  previous: Précédent
  select_option: Sélectionner une option
  sort: Trier
  sort_asc: Trier par ordre croissant
  sort_desc: Trier par ordre décroissant
  to: Jusqu'à
  today: Aujourd’hui
  today_minus: Aujourd’hui moins
  today_plus: Aujourd’hui plus
  week: Semaine
  where: Où
  year: Année
quick_access: Accès rapide
recently_used: Récemment utilisés
records_shared: Enregistrements partagés avec cette équipe
records_subteams: Enregistrements des sous-équipes
refresh: Rafraîchir
remove_interface_from_team: Retirer l'interface de cette équipe
remove_object_from_team: Retirer l'objet de cette équipe
revision_history:
  title: Historique des révisions
  user:
    create: "<b>{user}</b> a créé cet enregistrement"
    update: "<b>{user}</b> a modifié cet enregistrement"
  you:
    create: "<b>Vous</b> avez créé cet enregistrement"
    update: "<b>Vous</b> avez édité cet enregistrement"
role:
  access_rights: Droits d'accès
  assigned_to: Assigné à
  author_of_a_record: Auteur d'un enregistrement
  edit_role: Modifier le rôle
  fixed_set_of_users: Ensemble fixe d'utilisateurs
  new_role: Nouveau rôle
  role: Rôle
  through_attribute: A travers le champ
  users_from_specific_field: Utilisateurs d'un champ spécifique
roles: Rôles
root_level: Niveau racine
save: Sauvegarder
saving: Sauvegarde en cours
scan: Scanner
search: Rechercher
search_icons: Rechercher icônes
section: Section
section_name: Nom de la section
select_color: Sélectionnez la couleur
select_column_header: Sélectionnez un en-tête de colonne
select_column_header_description: Sélectionnez un champ à utiliser pour regrouper les enregistrements dans cette vue {viewType}
select_create_which_object: Sélectionnez l'objet que vous souhaitez créer
select_date: Choisir une date
select_field: Sélectionner un champ
select_filter: Sélectionnez le filtre
select_object: Sélectionner un objet
select_placeholder: Faire un choix
select_record: Sélectionner un enregistrement
select_tab: Sélectionner un onglet
select_team: Sélectionner une équipe
select_user: Sélectionner un utilisateur
selectable_values: Valeurs sélectionnables
send_email_to: Envoyer un e-mail à
settings: Réglages
share: Partager
share_interface_with_new_teams: Partager l'interface avec les équipes
share_object_with_new_teams: Partager un objet avec des équipes
show_all: Afficher tout
show_background_image: Afficher l'image d'arrière-plan
show_details: Afficher les détails
show_details_in_header: Afficher les détails dans l'en-tête
show_field_names: Afficher les noms des champs
show_more: Afficher plus
show_options: 'Afficher les options :'
show_thumbnail_icon: Afficher l'icône de l'objet
sign_here: Signer ici
sign_out: Déconnexion
single: Unique
size_limit_warning: Limite de taille dépassée
someone: Quelqu'un
sort_rules_applied: "{amount, plural, =0 {Aucune règle de tri appliquée} =1 {# règle de tri appliquée} other {# règles de tri appliquées}}"
start_houston: Commencer à utiliser Houston
status: Statut
step: Intervalle
stop_scanning: Arrêter le scan
structure: Structure
subject: Sujet
submit: Envoyer
subteams: Sous-équipes
subtitle: Sous-titre
support: Support
system: Système
tab: Onglet
tab_name: Nom de l'onglet
team: Équipe
team_filterable: Filtrer les enregistrements en fonction de l’équipe sélectionnée
team_filterable_user_relationship: Filtrer les utilisateurs en fonction de l'équipe sélectionnée
teams: Équipes
teams_to_scope_data_to: Données de
theme: Mode d'affichage
theme_dark: Sombre (expérimental)
theme_light: Clair
thumbnail:
  background_image: Image d’arrière-plan
  card: Carte
  date_time_display_mode:
    date_and_time: Date et heure
    date_only: Date uniquement
    time_only: Heure uniquement
  default_card: Utilisé par défaut dans la galerie.
  default_multiple_rows: Utilisé par défaut dans les listes et les formulaires.
  default_single_column: Peut être utilisé dans les formulaires.
  default_single_row: Utilisé par défaut dans les tables.
  edit_thumbnail: Modifier la miniature
  grid: Plusieurs lignes
  horizontal: Une seule ligne
  prefix: Préfixe
  row: Ligne
  selected_field: Champ de la miniature sélectionné
  show_title_row: Afficher ligne de titre
  subtitle: Sous-titre
  suffix: Suffixe
  thumbnail: Miniature
  title: Titre
  type: Type de miniature
  vertical: Seule colonne
thumbnails: Miniatures
time_unit: Unité de temps
timeline:
  end_date: Date de fin
  start_date: Date de début
timeline_settings: Paramètres de la chronologie
title: Titre
title_field: Champ de titre
translation_management: Gestion des traductions
'true': Oui
type: Type
unassigned: Non attribué
uncategorized: Non classé
unlink_selected: Dissocier les enregistrements sélectionnés
unpublish: Annuler la publication
unpublished: En attente de publication
unshare: Annuler la diffusion
uploader:
  action: Action
  one_or_more_files: Télécharger 1 ou plusieurs fichiers
  take_picture: Prendre photo
  take_video: Enregistrer vidéo
  uploading: Téléchargement en cours ...
used_in: Utilisé dans
user_account:
  activate: Activer le compte utilisateur
  deactivate: Désactiver le compte utilisateur
user_guide: Guide utilisateur
user_join_requests:
  approvals: Approbations
  approve: Approuver
  approve_requests: Approuver sélection
  awaiting_approval: En attente d'approbation
  date_and_time: Date et heure de la demande
  reject: Rejeter
  reject_requests: Rejeter sélection
  toast:
    body: Nous avons demandé aux administrateurs d'approuver votre demande d'adhésion.
    title: Demande d'adhésion envoyée
user_not_found: Utilisateur non trouvé?
users: Utilisateurs
validation: Validation
validation_type:
  equals: doit être égal à
  greater_than: doit être supérieur à
  less_than: doit être inférieur à
  max_length: ne peut pas être plus long que
  min_length: ne peut pas être plus court que
  other_than: doit être différent de
  presence: doit être rempli
validations: Validations
value: Valeur
value_set_on_creation: Valeur définie à la création
variants:
  master: Master
view: Vue
visibility: Visibilité
visualisation:
  multiObjectList: Liste multi-objets
  objectCalendar: Calendrier
  objectGallery: Gallerie
  objectKanban: Kanban
  objectList: Liste
  objectTable: Table
  objectTimeline: Chronologie
warning_changes_object_type: Les modifications apportées à cet objet affecteront tous les enregistrements de cet objet. Procédez avec précaution !
warning_optional_field: Ce champ est facultatif et n'est peut-être pas disponible dans les variantes.
welcome: Bienvenue à Houston. Pour commencer à utiliser Houston, veuillez demander à rejoindre une équipe
