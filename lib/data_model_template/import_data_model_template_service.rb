module DataModelTemplate
  class ImportDataModelTemplateService < ApplicationService
    def initialize(template, reference_map:, user: nil, no_safety_checks: false)
      super()
      @template = template
      @reference_map = reference_map
      @user = user
      @no_safety_checks = no_safety_checks
    end

    def execute
      ActiveRecord::Base.transaction do
        unhandled_attribute_templates = handle_object_type_templates!
        handle_unhandled_attribute_templates!(unhandled_attribute_templates)
        unhandled_attribute_templates.keys
      end
    end

    private

    attr_reader :template, :reference_map, :user, :no_safety_checks

    def validate_template!
      schemer = DataModelTemplate::Schemas.get_schema('data_model_schema')
      errors = schemer.validate(template).to_a
      raise DataModelTemplate::Errors::InvalidTemplateError.new('DataModelTemplate', template, errors) if errors.present?
    end

    def handle_object_type_templates!
      unhandled_attribute_templates = {}
      template.each do |object_type_template|
        object_type_template = object_type_template.with_indifferent_access
        if object_type_template.key?(:id)
          object_type = ::NPlusOne.ignore do
            Custom::ObjectType.find(object_type_template[:id])
          end
        end

        result = DataModelTemplate::ObjectType::CreateOrUpdateFromTemplateService.execute(
          object_type_template,
          object_type,
          user: user,
          reference_map: reference_map,
          no_safety_checks: no_safety_checks
        )

        unhandled_attribute_templates[result[:object_type]] = result[:unhandled_attribute_templates]
      end
      unhandled_attribute_templates
    end

    def handle_unhandled_attribute_templates!(unhandled_attribute_templates)
      unhandled_attribute_templates.each do |object_type, attribute_templates|
        next if attribute_templates.blank?

        attribute_templates.each do |attribute_template|
          attribute = object_type.object_type_attributes.find { |ota| ota.id == attribute_template[:id] }
          DataModelTemplate::ObjectTypeAttribute::CreateOrUpdateFromTemplateService.execute(
            attribute_template,
            attribute,
            object_type: object_type,
            reference_map: reference_map,
            no_safety_checks: no_safety_checks
          )
        rescue DataModelTemplate::Errors::UnresolvedReferenceError => e
          Rails.logger.info reference_map
          raise DataModelTemplate::Errors::InvalidTemplateError.new('ObjectTypeAttribute', attribute_template, e.message)
        end
      end
    end
  end
end
