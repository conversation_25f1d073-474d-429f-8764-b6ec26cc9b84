module DataModelTemplate
  class ObjectTypeAttribute::CreateOrUpdateFromTemplateService < ApplicationService
    def initialize(template, object_type_attribute, object_type: nil, reference_map: {}, no_safety_checks: false)
      super()
      @template = template.deep_dup
      @object_type_attribute = object_type_attribute
      @object_type = object_type
      @reference_map = reference_map
      @no_safety_checks = no_safety_checks
    end

    def execute
      safe_attribute_change_check!
      resolve_references!

      ActiveRecord::Base.transaction do
        create_or_update_object_type_attribute!
      rescue StandardError => e
        Rails.logger.error JSON.pretty_generate(template)
        raise e
      end
    end

    private

    attr_reader :template, :object_type_attribute, :object_type, :reference_map, :no_safety_checks,
                :errors

    def safe_attribute_change_check!
      return if object_type_attribute.nil?
      return if no_safety_checks

      key = object_type_attribute.key
      violations = []
      unsafe_properties = [
        ['data_type', object_type_attribute.data_type, template[:data_type]],
        ['relationship_kind', object_type_attribute.relationship_kind, template[:relationship_kind]],
        ['target_class', object_type_attribute.target_class, template[:target_class]],
        ['target_object_type_id', object_type_attribute.target_object_type_id, template[:target_object_type_id]],
        ['allowed_values.type', object_type_attribute.allowed_values&.fetch('type'), template.dig(:allowed_values, :type)]
      ]

      unsafe_properties.each do |name, old, new|
        next if old == new
        next if new.blank?

        violations << DataModelTemplate::Errors::UnsafeUpdateViolation.new(key, name, old, new)
      end

      if template.dig(:allowed_values, :values)
        template_values = template.dig(:allowed_values, :values).pluck(:value)
        selectable_values = object_type_attribute.object_type_attribute_selectable_values.pluck(:value)
        (selectable_values - template_values).each do |missing_value|
          violations << DataModelTemplate::Errors::DestructiveUpdateViolation.new(key, missing_value)
        end
      end

      raise DataModelTemplate::Errors::UnsafeUpdateError, violations if violations.present?
    end

    def resolve_references!
      references = [:target_object_type_reference, :target_object_type_attribute_reference]
      unresolved_references = references.filter_map do |key|
        next unless template.key?(key)

        id = template[key]
        if reference_map.key?(id)
          template[reference_to_id(key)] = reference_map[id].id
          template.delete(key)
          next
        else
          id
        end
      end

      raise DataModelTemplate::Errors::UnresolvedReferenceError, unresolved_references if unresolved_references.present?
    end

    def reference_to_id(key)
      key.to_s.sub('_reference', '_id').to_sym
    end

    def create_or_update_object_type_attribute!
      if object_type_attribute.present?
        if object_type_attribute_params.present?
          Custom::ObjectTypeAttribute::UpdateService.execute(object_type_attribute, payload: object_type_attribute_params)
        end
      else
        key = generate_key
        object_type_attribute_params[:attributes].merge!({ key: key, field_identifier: key })
        @object_type_attribute = Custom::ObjectTypeAttribute::CreateService.execute(
          object_type_attribute_params,
          object_type: object_type
        )
      end

      reference_map[template[:reference]] = object_type_attribute if template.key?(:reference)
    end

    def generate_key
      name = I18n.available_locales.lazy.map do |locale|
        template[:"name_#{locale}"]
      end.first
      if name.blank?
        raise DataModelTemplate::Errors::InvalidTemplateError.new(
          'ObjectTypeAttribute',
          template,
          'Name must be present for a new ota'
        )
      end

      Utility.keyify_string(name)
    end

    def add_error(error)
      @errors ||= DataModelTemplate::UnsafeUpdateError.new
      @errors.merge!(error)
    end

    def object_type_attribute_params
      @object_type_attribute_params ||= begin
        attributes = template.except(:id, :reference)
        condense_localised_attributes(attributes)
        add_default_attributes(attributes)
        if attributes.present?
          ActionController::Parameters.new(
            { attributes: attributes }
          )
        end
      end
    end

    def condense_localised_attributes(attributes)
      name = attributes[:name_en] || attributes[:name_fr] || attributes[:name_nl]
      attributes[:name] = name if name.present?

      selectable_values = attributes.dig(:allowed_values, :values)
      return if selectable_values.blank?

      selectable_values.each do |sv|
        text = sv[:text_en] || sv[:text_fr] || sv[:text_nl]
        sv[:text] = text if text.present?
      end
    end

    def add_default_attributes(attributes)
      return unless attributes[:data_type] == 'Relationship'

      attributes[:relationship_kind] ||= 'multiple'
      attributes[:inverse_relationship_kind] ||= 'multiple'
      return if attributes[:target_object_type_attribute_id].present?

      attributes[:target_column_name] ||= attributes[:target_class] == 'User' ? 'name' : 'id'
    end
  end
end
