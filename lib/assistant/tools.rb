module Assistant
  module Tools
    def self.build_tools_for_chat(chat)
      tools = tools_for_chat(chat)
      tools.map do |tool_name|
        tool_class = tool_name.constantize
        if tool_class.respond_to?(:build_for_chat)
          tool_class.build_for_chat(chat)
        else
          tool_class.new
        end
      end
    end

    def self.run_tools(assistant, tool_calls)
      tool_calls.each do |tool_call|
        run_tool(assistant, tool_call)
      end
    end

    def self.run_tool(assistant, tool_call)
      tool_call_id, tool_name, method_name, tool_arguments = assistant.llm_adapter.extract_tool_call_args(tool_call: tool_call)
      tool_instance = assistant.tools.find do |t|
        t.class.tool_name == tool_name
      end or raise ArgumentError, "Tool: #{tool_name} not found in assistant.tools"

      output = tool_instance.send(method_name, **tool_arguments)
      {
        tool_name: "#{tool_name}__#{method_name}",
        tool_call_id: tool_call_id,
        arguments: tool_arguments,
        output: output
      }
    end

    def self.tools_for_chat(chat)
      case chat.context['intent']
      when 'app_creation'
        [
          'Assistant::Tools::DescribeAppTool',
          'Assistant::Tools::SuggestDataModelTool',
          'Assistant::Tools::KpisTool'
        ]
      when 'data_analysis'
        ['Assistant::Tools::RecordLoadingTool']
      else
        []
      end
    end
  end
end
