require 'langchain'

module Assistant
  module Tools
    class DescribeAppTool
      extend Langchain::ToolDefinition

      SCHEMA = <<~JSON
        {
          "$defs": {
            "step": {
              "type": "object",
              "description": "A single step in the app workflow",
              "properties": {
                "step": {
                  "type": "string",
                  "description": "The step number or identifier"
                },
                "description": {
                  "type": "string",
                  "description": "Detailed description of what happens in this step"
                },
                "actor": {
                  "type": "string",
                  "description": "The role or person responsible for executing this step"
                }
              },
              "required": ["step", "description", "actor"]
            }
          },
          "type": "object",
          "description": "A description of an app, the objects it contains and how these objects should be used.",
          "properties": {
            "short_description": {
              "type": "string",
              "description": "A brief overview of this app summarized in 1-2 lines",
              "maxLength": 200
            },
            "long_description": {
              "type": "string",
              "description": "A comprehensive overview of this app with introduction and key points"
            },
            "key_roles": {
              "type": "array",
              "description": "List of main roles involved in this app",
              "items": {
                "type": "string",
                "description": "A role or position involved in the app"
              }
            },
            "steps": {
              "type": "array",
              "description": "Step-by-step description of the app workflow",
              "items": {
                "$ref": "#/$defs/step"
              }
            }
          },
          "required": ["short_description", "long_description", "key_roles", "steps"],
          "additionalProperties": false
        }

      JSON
               .freeze

      define_function_with_schema(
        :execute,
        description: 'Presents a description of an app and its datamodel to the user.' \
                     'Use this tool to show a summary to the user',
        params: JSON.parse(SCHEMA)
      )

      def execute(**params)
        Assistant::Tools::Response.new(
          type: :data,
          content: 'The app description is shown to the user',
          data: params
        )
      end
    end
  end
end
