{"type": "object", "properties": {"objects": {"$id": "DataModelSchema", "type": "array", "description": "the object types in the data model", "items": {"type": "object", "description": "specification for a single object type", "properties": {"id": {"type": "number", "description": "the id of an object type that already exists in the data model"}, "reference": {"type": "string", "format": "uuid", "description": "a globally unique identifier used to refer to this object type in json documents"}, "name_en": {"type": "string", "description": "name of the object type in English"}, "name_fr": {"type": "string", "description": "name of the object type in French"}, "name_nl": {"type": "string", "description": "name of the object type in Dutch"}, "icon": {"type": "string", "pattern": "^fa fa-[a-z-]+$", "description": "fontawesome icon of the object type"}, "color": {"type": "string", "pattern": "^#[0-9a-fA-F]{6}$", "description": "hex representation of a color used to display the object type"}, "description": {"type": "string", "description": "description of the object type and its usage"}, "attributes": {"type": "array", "description": "attributes of the object type", "items": {"type": "object", "description": "specification for a single attribute", "properties": {"id": {"type": "number", "description": "the id of an attribute that already exists in the data model"}, "reference": {"type": "string", "format": "uuid", "description": "a globally unique identifier used to refer to this attribute in json documents"}, "name_en": {"type": "string", "description": "name of the attribute in English"}, "name_fr": {"type": "string", "description": "name of the attribute in French"}, "name_nl": {"type": "string", "description": "name of the attribute in Dutch"}, "description": {"type": "string", "description": "description of the field and its usage"}, "data_type": {"type": "string", "description": "data type of the attribute", "enum": ["String", "Boolean", "Date", "DateTime", "Number", "HTML", "Relationship", "Attachment"]}, "relationship_kind": {"type": "string", "description": "cardinality of the relationship", "enum": ["single", "multiple"]}, "inverse_relationship_kind": {"type": "string", "description": "cardinality of the inverse relationship", "enum": ["single", "multiple"]}, "target_class": {"type": "string", "description": "whether the relationship is to a user or an object type", "enum": ["User", "Object"]}, "target_object_type_reference": {"type": "string", "description": "Reference of the target object type of the relationship. Omit this field if target_class is User."}, "allowed_values": {"type": "object", "description": "specification of selectable options for a dropdown attribute", "properties": {"values": {"type": "array", "description": "list of selectable options", "items": {"type": "object", "properties": {"value": {"type": "string", "description": "value of the option"}, "text_en": {"type": "string", "description": "displayed text of the option in English"}, "text_fr": {"type": "string", "description": "displayed text of the option in French"}}, "required": ["value", "text_en", "text_fr"]}}}}}, "additionalProperties": false, "required": ["reference", "name_en", "name_fr", "data_type", "description"]}}}, "additionalProperties": false, "required": ["reference", "attributes", "name_en", "name_fr", "icon", "color", "description"]}}}, "additionalProperties": false, "required": ["objects"]}